<?php

namespace console\controllers;

use app_admin\services\mall\BusinessService;
use common\libraries\DocxProcessor;
use common\models\Organize;
use common\models\OrganizeBrand;
use yii\console\Controller;


class TestController extends Controller
{
    public function actionAddBrand()
    {
        // 为所有组织添加品牌权限
        $organizes = Organize::find()->all();
        foreach ($organizes as $organize) {
            $organize_brands = OrganizeBrand::find()->where(['org_id' => $organize->org_id])->all();
            $brand_ids = array_column($organize_brands, 'brand_id');
            // 如果不包含 品牌ID 1 则添加
            if (!in_array(1, $brand_ids)) {
                $organize_brand = new OrganizeBrand();
                $organize_brand->org_id = $organize->org_id;
                $organize_brand->brand_id = 1;
                $organize_brand->save();
            }
        }
        echo 'done';
    }
    public function actionIndex()
    {
        $organizes = Organize::find()->where(['parent_id' => 1])->all();
        $acl_id = '2047';
        foreach ($organizes as $organize) {
            $acls = $organize->acl_ids;

            $acls_array = unserialize($acls);
            if (!is_array($acls_array)) {
                continue;
            }

            // 添加查看的权限
            $acls_array[$acl_id] = '1';
            $acls = serialize($acls_array);
            $organize->acl_ids = $acls;
            $organize->save();
            echo $organize->scname . ' done' . PHP_EOL;
        }
    }

    /**
     * 导出人员名称+权限名称的Excel功能
     * 排除离职人员
     *
     * 使用方法: php yii test/export-user-permissions
     *
     * 功能说明:
     * 1. 查询所有在职人员（同时检查User表的leave_office_date=0和DingtalkUser表的delete_time）
     * 2. 获取每个人员的权限信息
     * 3. 将权限路由转换为中文名称
     * 4. 导出到Excel文件，包含：姓名、用户名、手机号、权限名称
     * 5. 文件保存在项目根目录，文件名包含时间戳
     *
     * @return void
     */
    public function actionExportUserPermissions()
    {
        echo "开始导出人员权限信息..." . PHP_EOL;

        // 获取所有在职用户（排除离职人员）
        // 需要同时检查 User 表的 leave_office_date 和 DingtalkUser 表的 delete_time
        $all_users = $this->getActiveUsers();

        echo "找到在职用户数量: " . count($all_users) . PHP_EOL;

        // 排除拥有 system:admin 或 system:test 权限的用户
        $users = [];
        $excluded_admin_count = 0;
        foreach ($all_users as $user) {
            if (\common\models\User::checkUserAclRoutes($user->user_id, 'system:admin', 'system:test')) {
                $excluded_admin_count++;
                continue;
            }
            $users[] = $user;
        }

        echo "排除管理员/测试用户数量: " . $excluded_admin_count . PHP_EOL;
        echo "最终导出用户数量: " . count($users) . PHP_EOL;

        $excel_data = [];
        $no_permission_count = 0; // 无权限用户计数

        foreach ($users as $user) {
            // 获取用户的权限路由
            $acl_routes = \common\models\User::getUserAclRoutes($user->user_id);

            // 通过路由获取权限中文名称
            $acl_names = [];
            if (!empty($acl_routes)) {
                $acls = \common\models\Acl::find()
                    ->where(['route' => $acl_routes])
                    ->select(['scname', 'route'])
                    ->asArray()
                    ->all();

                foreach ($acls as $acl) {
                    if (!empty($acl['scname'])) {
                        $acl_names[] = $acl['scname'];
                    }
                }
            }

            // 如果用户没有权限，显示"无权限"
            if (empty($acl_names)) {
                $permission_text = '无权限';
                $no_permission_count++;
            } else {
                $permission_text = implode(', ', array_unique($acl_names));
            }

            $excel_data[] = [
                $user->scname ?: '未知姓名',
                $user->username ?: '',
                $user->mobile ?: '',
                $permission_text
            ];
        }

        echo "无权限用户数量: " . $no_permission_count . PHP_EOL;

        // 表头
        $excel_header = [
            '姓名',
            '用户名',
            '手机号',
            '权限名称'
        ];

        // 表格配置
        $excel_extra = [
            'filename' => '人员权限导出_' . date('Y-m-d_H-i-s'),
            'title' => '人员权限列表',
        ];

        // 列宽设置
        $colWidth = [
            'A' => 15, // 姓名
            'B' => 20, // 用户名
            'C' => 15, // 手机号
            'D' => 80, // 权限名称
        ];

        // 字符串列（防止科学计数法）
        $stringColumn = ['C']; // 手机号设为字符串格式

        // 为命令行环境设置文件路径和名称
        $filename = '人员权限导出_' . date('Y-m-d_H-i-s');
        $current_dir = dirname(__FILE__) . '/../../'; // 项目根目录

        $stringColumn['filename'] = $filename;
        $stringColumn['filepath'] = $current_dir;
        $stringColumn['ext'] = 'xlsx';

        try {
            // 使用newFile方法生成文件而不是直接输出
            $result_filename = \common\libraries\Excel::newFile($excel_header, $excel_data, $colWidth, 0, $stringColumn, $excel_extra);

            echo '导出完成! 文件保存到: ' . $current_dir . $result_filename . PHP_EOL;
            echo '统计信息:' . PHP_EOL;
            echo '  - 总用户数: ' . count($users) . PHP_EOL;
            echo '  - 有权限用户数: ' . (count($users) - $no_permission_count) . PHP_EOL;
            echo '  - 无权限用户数: ' . $no_permission_count . PHP_EOL;
        } catch (\Exception $e) {
            echo '导出失败: ' . $e->getMessage() . PHP_EOL;
        }
    }

    /**
     * 导出人员权限详细信息Excel - 优化版
     * 排除系统(acl_id=8)和用户(acl_id=1)权限类别
     *
     * 使用方法: php yii test/export-user-permissions-detailed
     *
     * 功能说明:
     * 1. 查询所有在职人员（同时检查User表的leave_office_date=0和DingtalkUser表的delete_time）
     * 2. 获取权限信息，排除系统和用户类别权限
     * 3. 按权限分类展示，便于筛选
     * 4. 每个用户一行，每个权限一列，方便Excel筛选
     *
     * @return void
     */
    public function actionExportUserPermissionsDetailed()
    {
        echo "开始导出人员权限详细信息..." . PHP_EOL;

        // 获取所有在职用户（排除离职人员）
        // 需要同时检查 User 表的 leave_office_date 和 DingtalkUser 表的 delete_time
        $all_users = $this->getActiveUsers();

        echo "找到在职用户数量: " . count($all_users) . PHP_EOL;

        // 排除拥有 system:admin 或 system:test 权限的用户
        $users = [];
        $excluded_admin_count = 0;
        foreach ($all_users as $user) {
            if (\common\models\User::checkUserAclRoutes($user->user_id, 'system:admin', 'system:test')) {
                $excluded_admin_count++;
                continue;
            }
            $users[] = $user;
        }

        echo "排除管理员/测试用户数量: " . $excluded_admin_count . PHP_EOL;
        echo "最终导出用户数量: " . count($users) . PHP_EOL;

        // 获取所有权限，排除系统和用户类别
        $excluded_parent_ids = [1, 8]; // 用户和系统

        // 递归获取需要排除的所有权限ID（包括子权限）
        $excluded_acl_ids = $this->getExcludedAclIds($excluded_parent_ids);
        echo "排除的权限数量: " . count($excluded_acl_ids) . PHP_EOL;

        // 获取有效权限列表
        $valid_acls = \common\models\Acl::find()
            ->where(['not in', 'acl_id', $excluded_acl_ids])
            ->andWhere(['!=', 'route', '']) // 排除空路由
            ->orderBy('parent_id ASC, order_by ASC')
            ->asArray()
            ->all();

        echo "有效权限数量: " . count($valid_acls) . PHP_EOL;

        // 构建权限映射
        $acl_map = [];
        foreach ($valid_acls as $acl) {
            $acl_map[$acl['route']] = $acl['scname'];
        }

        // 构建Excel数据
        $excel_data = [];

        foreach ($users as $user) {
            $user_acl_routes = \common\models\User::getUserAclRoutes($user->user_id);

            // 过滤掉被排除的权限
            $filtered_routes = array_intersect($user_acl_routes, array_keys($acl_map));

            // 基础用户信息
            $row_data = [
                $user->scname ?: '未知姓名',
                $user->username ?: '',
                $user->mobile ?: '',
                count($filtered_routes), // 权限数量
            ];

            // 为每个权限添加列（有权限标记为"✓"，无权限为空）
            foreach ($valid_acls as $acl) {
                $has_permission = in_array($acl['route'], $filtered_routes) ? '✓' : '';
                $row_data[] = $has_permission;
            }

            $excel_data[] = $row_data;
        }

        // 构建表头
        $excel_header = [
            '姓名',
            '用户名',
            '手机号',
            '权限数量'
        ];

        // 添加权限列标题
        foreach ($valid_acls as $acl) {
            $parent_name = '';
            if ($acl['parent_id'] > 0) {
                $parent_acl = \common\models\Acl::find()->where(['acl_id' => $acl['parent_id']])->one();
                if ($parent_acl) {
                    $parent_name = $parent_acl->scname . ' - ';
                }
            }
            $excel_header[] = $parent_name . $acl['scname'];
        }

        // 表格配置
        $excel_extra = [
            'filename' => '人员权限详细信息_' . date('Y-m-d_H-i-s'),
            'title' => '人员权限详细信息',
        ];

        // 列宽设置
        $colWidth = [
            'A' => 15, // 姓名
            'B' => 20, // 用户名
            'C' => 15, // 手机号
            'D' => 12, // 权限数量
        ];

        // 权限列设置较窄的宽度
        $col_index = 5; // 从E列开始
        foreach ($valid_acls as $acl) {
            $col_letter = $this->numberToColumnLetter($col_index);
            $colWidth[$col_letter] = 8;
            $col_index++;
        }

        // 字符串列
        $stringColumn = ['C']; // 手机号

        // 为命令行环境设置文件路径和名称
        $filename = '人员权限详细信息_' . date('Y-m-d_H-i-s');
        $current_dir = dirname(__FILE__) . '/../../';

        $stringColumn['filename'] = $filename;
        $stringColumn['filepath'] = $current_dir;
        $stringColumn['ext'] = 'xlsx';

        try {
            // 先简化，不使用复杂的回调函数
            $result_filename = \common\libraries\Excel::newFile(
                $excel_header,
                $excel_data,
                $colWidth,
                0,
                $stringColumn,
                $excel_extra
            );

            echo '导出完成! 文件保存到: ' . $current_dir . $result_filename . PHP_EOL;
            echo '统计信息:' . PHP_EOL;
            echo '  - 总用户数: ' . count($users) . PHP_EOL;
            echo '  - 有效权限数: ' . count($valid_acls) . PHP_EOL;
            echo '  - 排除权限数: ' . count($excluded_acl_ids) . PHP_EOL;
        } catch (\Exception $e) {
            echo '导出失败: ' . $e->getMessage() . PHP_EOL;
        }
    }

    /**
     * 递归获取需要排除的权限ID（包括子权限）
     */
    private function getExcludedAclIds($parent_ids)
    {
        $excluded_ids = $parent_ids;

        foreach ($parent_ids as $parent_id) {
            $child_acls = \common\models\Acl::find()
                ->where(['parent_id' => $parent_id])
                ->select('acl_id')
                ->column();

            if (!empty($child_acls)) {
                $excluded_ids = array_merge($excluded_ids, $child_acls);
                // 递归查找子权限的子权限
                $excluded_ids = array_merge($excluded_ids, $this->getExcludedAclIds($child_acls));
            }
        }

        return array_unique($excluded_ids);
    }

    /**
     * 数字转Excel列字母
     */
    private function numberToColumnLetter($num)
    {
        $letter = '';
        while ($num > 0) {
            $num--;
            $letter = chr($num % 26 + 65) . $letter;
            $num = intval($num / 26);
        }
        return $letter;
    }

    /**
     * 获取所有在职用户（用于计算数据范围）
     * 需要同时检查 User 表的 leave_office_date 和 DingtalkUser 表的 delete_time
     */
    private function getAllUsers()
    {
        return $this->getActiveUsers();
    }

    /**
     * 获取所有在职用户的通用方法
     * 同时检查 User 表的 leave_office_date 和 DingtalkUser 表的 delete_time 字段
     *
     * @return \common\models\User[]
     */
    private function getActiveUsers()
    {
        return \common\models\User::find()
            ->leftJoin('dingtalk_user du', 'user.ding_userid = du.id')
            ->where(['user.leave_office_date' => 0]) // User表中没有离职日期的用户
            ->andWhere([
                'or',
                ['du.delete_time' => null], // 钉钉用户表中没有记录
                ['du.delete_time' => '0000-00-00 00:00:00'] // 钉钉用户表中未离职
            ])
            ->orderBy('user.scname ASC') // 按姓名排序
            ->all();
    }

    /**
     * 导出权限列表 - 查看所有有效权限
     * 
     * 使用方法: php yii test/export-permissions-list
     * 
     * 功能说明:
     * 1. 导出所有有效权限的列表
     * 2. 排除系统和用户类别权限
     * 3. 显示权限层级关系
     * 4. 便于了解系统中有哪些权限
     * 
     * @return void
     */
    public function actionExportPermissionsList()
    {
        echo "开始导出权限列表..." . PHP_EOL;

        // 获取需要排除的权限ID
        $excluded_parent_ids = [1, 8]; // 用户和系统
        $excluded_acl_ids = $this->getExcludedAclIds($excluded_parent_ids);

        // 获取所有有效权限
        $all_acls = \common\models\Acl::find()
            ->where(['not in', 'acl_id', $excluded_acl_ids])
            ->orderBy('parent_id ASC, order_by ASC')
            ->asArray()
            ->all();

        echo "有效权限总数: " . count($all_acls) . PHP_EOL;

        // 构建权限层级结构
        $excel_data = [];

        foreach ($all_acls as $acl) {
            $parent_name = '';
            $level = 0;

            if ($acl['parent_id'] > 0) {
                $parent_acl = \common\models\Acl::find()->where(['acl_id' => $acl['parent_id']])->one();
                if ($parent_acl) {
                    $parent_name = $parent_acl->scname;
                    $level = 1;

                    // 检查是否还有上级
                    if ($parent_acl->parent_id > 0) {
                        $grandparent = \common\models\Acl::find()->where(['acl_id' => $parent_acl->parent_id])->one();
                        if ($grandparent) {
                            $parent_name = $grandparent->scname . ' > ' . $parent_name;
                            $level = 2;
                        }
                    }
                }
            }

            $excel_data[] = [
                $acl['acl_id'],
                $parent_name,
                $acl['scname'],
                $acl['route'] ?: '',
                $level == 0 ? '顶级' : ($level == 1 ? '二级' : '三级'),
                $acl['is_menu'] ? '是' : '否',
                $acl['description'] ?: ''
            ];
        }

        // 表头
        $excel_header = [
            '权限ID',
            '上级权限',
            '权限名称',
            '路由',
            '层级',
            '是否菜单',
            '描述'
        ];

        // 表格配置
        $excel_extra = [
            'filename' => '权限列表_' . date('Y-m-d_H-i-s'),
            'title' => '权限列表',
        ];

        // 列宽设置
        $colWidth = [
            'A' => 10, // 权限ID
            'B' => 25, // 上级权限
            'C' => 30, // 权限名称
            'D' => 35, // 路由
            'E' => 10, // 层级
            'F' => 12, // 是否菜单
            'G' => 40, // 描述
        ];

        // 字符串列
        $stringColumn = ['A', 'D']; // 权限ID和路由

        // 设置文件路径和名称
        $filename = '权限列表_' . date('Y-m-d_H-i-s');
        $current_dir = dirname(__FILE__) . '/../../';

        $stringColumn['filename'] = $filename;
        $stringColumn['filepath'] = $current_dir;
        $stringColumn['ext'] = 'xlsx';

        try {
            $result_filename = \common\libraries\Excel::newFile(
                $excel_header,
                $excel_data,
                $colWidth,
                0,
                $stringColumn,
                $excel_extra
            );

            echo '导出完成! 文件保存到: ' . $current_dir . $result_filename . PHP_EOL;
            echo '统计信息:' . PHP_EOL;
            echo '  - 有效权限数: ' . count($all_acls) . PHP_EOL;
            echo '  - 排除权限数: ' . count($excluded_acl_ids) . PHP_EOL;
            echo '  - 有路由的权限数: ' . count(array_filter($all_acls, function ($acl) {
                return !empty($acl['route']);
            })) . PHP_EOL;
        } catch (\Exception $e) {
            echo '导出失败: ' . $e->getMessage() . PHP_EOL;
        }
    }

    /**
     * 查看被排除的管理员用户列表
     *
     * 使用方法: php yii test/show-excluded-admins
     *
     * 功能说明:
     * 1. 显示所有拥有 system:admin 或 system:test 权限的用户（同时检查User表的leave_office_date=0和DingtalkUser表的delete_time）
     * 2. 用于验证过滤功能是否正确
     *
     * @return void
     */
    public function actionShowExcludedAdmins()
    {
        echo "查看拥有管理员权限的用户..." . PHP_EOL;

        // 获取所有在职用户（排除离职人员）
        // 需要同时检查 User 表的 leave_office_date 和 DingtalkUser 表的 delete_time
        $all_users = $this->getActiveUsers();

        echo "总在职用户数量: " . count($all_users) . PHP_EOL;

        // 查找拥有管理员权限的用户
        $admin_users = [];
        foreach ($all_users as $user) {
            $has_admin = \common\models\User::checkUserAclRoutes($user->user_id, 'system:admin');
            $has_test = \common\models\User::checkUserAclRoutes($user->user_id, 'system:test');

            if ($has_admin || $has_test) {
                $permissions = [];
                if ($has_admin)
                    $permissions[] = 'system:admin';
                if ($has_test)
                    $permissions[] = 'system:test';

                $admin_users[] = [
                    'name' => $user->scname ?: '未知姓名',
                    'username' => $user->username ?: '',
                    'permissions' => implode(', ', $permissions)
                ];
            }
        }

        echo "拥有管理员权限的用户数量: " . count($admin_users) . PHP_EOL;
        echo str_repeat('-', 60) . PHP_EOL;
        echo sprintf("%-20s %-20s %-20s", "姓名", "用户名", "权限") . PHP_EOL;
        echo str_repeat('-', 60) . PHP_EOL;

        foreach ($admin_users as $user) {
            echo sprintf(
                "%-20s %-20s %-20s",
                mb_substr($user['name'], 0, 8),
                mb_substr($user['username'], 0, 15),
                $user['permissions']
            ) . PHP_EOL;
        }

        echo str_repeat('-', 60) . PHP_EOL;
        echo "普通用户数量: " . (count($all_users) - count($admin_users)) . PHP_EOL;
    }

    /**
     * 获取用户的全部权限并合并到另外一个组织结构里，同时管理用户组织关系（支持批量用户）
     *
     * 使用方法:
     * - 单个用户: php yii test/copy-user-permissions 265 201
     * - 批量用户: php yii test/copy-user-permissions "265,266,267" 201
     *
     * 功能说明:
     * 1. 获取源用户在所有组织结构中的权限
     * 2. 将这些权限与目标组织现有权限合并（不覆盖）
     * 3. 支持普通权限和主管权限的合并
     * 4. 将用户加入目标组织结构
     * 5. 移除用户在其他组织结构中的关系
     * 6. 支持批量处理多个用户
     * 7. 记录操作日志和统计信息
     *
     * @param string $source_user_ids 源用户ID（单个ID或逗号分隔的多个ID）
     * @param int $target_org_id 目标组织结构ID
     * @return void
     */
    public function actionCopyUserPermissions($source_user_ids = null, $target_org_id = null)
    {
        // 参数验证
        if (empty($source_user_ids) || empty($target_org_id)) {
            echo "错误: 请提供必要的参数" . PHP_EOL;
            echo "使用方法: php yii test/copy-user-permissions <源用户ID> <目标组织ID>" . PHP_EOL;
            echo "示例: " . PHP_EOL;
            echo "  单个用户: php yii test/copy-user-permissions 265 201" . PHP_EOL;
            echo "  批量用户: php yii test/copy-user-permissions \"265,266,267\" 201" . PHP_EOL;
            return;
        }

        // 解析用户ID列表
        $user_ids = array_map('trim', explode(',', $source_user_ids));
        $user_ids = array_filter($user_ids, function ($id) {
            return is_numeric($id) && $id > 0;
        });

        if (empty($user_ids)) {
            echo "错误: 没有有效的用户ID" . PHP_EOL;
            return;
        }

        echo "开始批量复制用户权限..." . PHP_EOL;
        echo "源用户ID: " . implode(', ', $user_ids) . " (共 " . count($user_ids) . " 个用户)" . PHP_EOL;
        echo "目标组织ID: {$target_org_id}" . PHP_EOL;
        echo str_repeat('-', 50) . PHP_EOL;

        // 验证所有源用户是否存在
        $source_users = \common\models\User::find()
            ->where(['user_id' => $user_ids])
            ->indexBy('user_id')
            ->all();

        $missing_users = array_diff($user_ids, array_keys($source_users));
        if (!empty($missing_users)) {
            echo "错误: 以下用户不存在: " . implode(', ', $missing_users) . PHP_EOL;
            return;
        }

        // 验证目标组织是否存在
        $target_org = Organize::findOne($target_org_id);
        if (!$target_org) {
            echo "错误: 目标组织不存在 (ID: {$target_org_id})" . PHP_EOL;
            return;
        }

        echo "源用户列表:" . PHP_EOL;
        foreach ($source_users as $user) {
            echo "  - {$user->scname} ({$user->username}) [ID: {$user->user_id}]" . PHP_EOL;
        }
        echo "目标组织: {$target_org->scname}" . PHP_EOL;
        echo str_repeat('-', 50) . PHP_EOL;

        // 显示所有用户的当前组织关系
        $all_current_orgs = \common\models\UserOrganize::find()
            ->leftJoin('organize', 'organize.org_id = user_organize.org_id')
            ->leftJoin('user', 'user.user_id = user_organize.user_id')
            ->where(['user_organize.user_id' => $user_ids])
            ->select(['user_organize.user_id', 'user.scname as user_name', 'organize.org_id', 'organize.scname as org_name'])
            ->asArray()
            ->all();

        if (!empty($all_current_orgs)) {
            echo "用户当前组织关系:" . PHP_EOL;
            $grouped_orgs = [];
            foreach ($all_current_orgs as $org) {
                $grouped_orgs[$org['user_id']][] = $org;
            }

            foreach ($grouped_orgs as $user_id => $orgs) {
                $user_name = $orgs[0]['user_name'];
                echo "  {$user_name} (ID: {$user_id}):" . PHP_EOL;
                foreach ($orgs as $org) {
                    echo "    - {$org['org_name']} (ID: {$org['org_id']})" . PHP_EOL;
                }
            }
        } else {
            echo "所有用户当前都不属于任何组织" . PHP_EOL;
        }

        echo str_repeat('-', 50) . PHP_EOL;
        echo "操作说明:" . PHP_EOL;
        echo "1. 获取所有用户在各自组织中的权限并合并" . PHP_EOL;
        echo "2. 将合并后的权限与目标组织现有权限合并（不覆盖原有权限）" . PHP_EOL;
        echo "3. 将所有用户加入目标组织" . PHP_EOL;
        echo "4. 移除所有用户在其他组织中的关系" . PHP_EOL;
        echo str_repeat('-', 50) . PHP_EOL;

        // 要求用户确认
        echo "请确认是否继续执行以上操作? (输入 y 确认，其他任意键取消): ";
        $handle = fopen("php://stdin", "r");
        $confirmation = trim(fgets($handle));
        fclose($handle);

        if (strtolower($confirmation) !== 'y') {
            echo "操作已取消。" . PHP_EOL;
            return;
        }

        echo "用户已确认，开始执行批量操作..." . PHP_EOL;
        echo str_repeat('=', 60) . PHP_EOL;

        try {
            // 收集所有用户的权限
            $all_permissions = ['acl_ids' => [], 'leader_acl_ids' => []];
            $processed_users = [];
            $failed_users = [];

            foreach ($user_ids as $user_id) {
                echo "处理用户 ID: {$user_id} ({$source_users[$user_id]->scname})" . PHP_EOL;
                echo str_repeat('-', 40) . PHP_EOL;

                try {
                    // 获取当前用户的所有权限
                    $user_permissions = $this->getUserAllPermissions($user_id);

                    if (empty($user_permissions['acl_ids']) && empty($user_permissions['leader_acl_ids'])) {
                        echo "  警告: 用户没有任何权限，跳过权限收集" . PHP_EOL;
                    } else {
                        // 合并权限到总权限集合中
                        foreach ($user_permissions['acl_ids'] as $acl_id => $status) {
                            if ($status == '1') {
                                $all_permissions['acl_ids'][$acl_id] = $status;
                            }
                        }
                        foreach ($user_permissions['leader_acl_ids'] as $acl_id => $status) {
                            if ($status == '1') {
                                $all_permissions['leader_acl_ids'][$acl_id] = $status;
                            }
                        }
                        echo "  权限收集完成" . PHP_EOL;
                    }

                    // 更新用户的组织关系
                    $org_result = $this->updateUserOrganization($user_id, $target_org_id);
                    if ($org_result['success']) {
                        echo "  组织关系更新成功 (移除 {$org_result['removed_count']} 个关系)" . PHP_EOL;
                        $processed_users[] = $user_id;
                    } else {
                        echo "  组织关系更新失败: " . $org_result['error'] . PHP_EOL;
                        $failed_users[] = $user_id;
                    }

                } catch (\Exception $e) {
                    echo "  处理失败: " . $e->getMessage() . PHP_EOL;
                    $failed_users[] = $user_id;
                }

                echo str_repeat('-', 40) . PHP_EOL;
            }

            // 将合并后的权限应用到目标组织
            echo "将合并后的权限应用到目标组织..." . PHP_EOL;
            echo "合并权限统计:" . PHP_EOL;
            echo "  普通权限数量: " . count($all_permissions['acl_ids']) . PHP_EOL;
            echo "  主管权限数量: " . count($all_permissions['leader_acl_ids']) . PHP_EOL;

            if (!empty($all_permissions['acl_ids']) || !empty($all_permissions['leader_acl_ids'])) {
                $result = $this->mergePermissionsToOrganize($all_permissions, $target_org_id);

                if ($result['success']) {
                    echo str_repeat('=', 60) . PHP_EOL;
                    echo "批量操作完成!" . PHP_EOL;
                    echo "权限合并结果:" . PHP_EOL;
                    echo "  合并的普通权限: " . $result['merged_acl_count'] . " 个" . PHP_EOL;
                    echo "  合并的主管权限: " . $result['merged_leader_acl_count'] . " 个" . PHP_EOL;
                    echo "  新增的普通权限: " . $result['new_acl_count'] . " 个" . PHP_EOL;
                    echo "  新增的主管权限: " . $result['new_leader_acl_count'] . " 个" . PHP_EOL;
                } else {
                    echo "错误: 权限合并失败 - " . $result['error'] . PHP_EOL;
                }
            } else {
                echo "警告: 没有有效权限需要合并" . PHP_EOL;
            }

            // 显示处理结果统计
            echo str_repeat('=', 60) . PHP_EOL;
            echo "处理结果统计:" . PHP_EOL;
            echo "  成功处理用户: " . count($processed_users) . " 个" . PHP_EOL;
            echo "  失败用户: " . count($failed_users) . " 个" . PHP_EOL;
            if (!empty($failed_users)) {
                echo "  失败用户ID: " . implode(', ', $failed_users) . PHP_EOL;
            }
            echo "  操作时间: " . date('Y-m-d H:i:s') . PHP_EOL;

        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . PHP_EOL;
            echo "堆栈跟踪: " . $e->getTraceAsString() . PHP_EOL;
        }
    }

    /**
     * 获取用户的所有权限（合并所有组织的权限）
     *
     * @param int $user_id 用户ID
     * @return array 包含 acl_ids 和 leader_acl_ids 的数组
     */
    private function getUserAllPermissions($user_id)
    {
        // 获取用户所属的所有组织
        $user_organizes = \common\models\UserOrganize::find()
            ->where(['user_id' => $user_id])
            ->all();

        if (empty($user_organizes)) {
            echo "警告: 用户不属于任何组织" . PHP_EOL;
            return ['acl_ids' => [], 'leader_acl_ids' => []];
        }

        $org_ids = array_column($user_organizes, 'org_id');
        echo "用户所属组织数量: " . count($org_ids) . PHP_EOL;

        // 获取所有组织的权限信息
        $organizes = Organize::find()
            ->where(['org_id' => $org_ids])
            ->all();

        $merged_acl_ids = [];
        $merged_leader_acl_ids = [];

        foreach ($organizes as $organize) {
            echo "处理组织: {$organize->scname} (ID: {$organize->org_id})" . PHP_EOL;

            // 处理普通权限
            if (!empty($organize->acl_ids)) {
                echo "  - 原始权限数据: " . $organize->acl_ids . PHP_EOL;
                $acl_ids = unserialize($organize->acl_ids);
                if (is_array($acl_ids)) {
                    echo "  - 反序列化成功，权限数量: " . count($acl_ids) . PHP_EOL;
                    // 使用 + 操作符合并数组，保持键值对结构
                    foreach ($acl_ids as $acl_id => $status) {
                        if ($status == '1') { // 只合并启用的权限
                            $merged_acl_ids[$acl_id] = $status;
                        }
                    }
                    echo "  - 有效普通权限: " . count(array_filter($acl_ids, function ($v) {
                        return $v == '1';
                    })) . " 个" . PHP_EOL;
                } else {
                    echo "  - 反序列化失败，数据类型: " . gettype($acl_ids) . PHP_EOL;
                }
            } else {
                echo "  - 无普通权限数据" . PHP_EOL;
            }

            // 处理主管权限
            if (!empty($organize->leader_acl_ids)) {
                echo "  - 原始主管权限数据: " . $organize->leader_acl_ids . PHP_EOL;
                $leader_acl_ids = unserialize($organize->leader_acl_ids);
                if (is_array($leader_acl_ids)) {
                    echo "  - 主管权限反序列化成功，权限数量: " . count($leader_acl_ids) . PHP_EOL;
                    // 使用 + 操作符合并数组，保持键值对结构
                    foreach ($leader_acl_ids as $acl_id => $status) {
                        if ($status == '1') { // 只合并启用的权限
                            $merged_leader_acl_ids[$acl_id] = $status;
                        }
                    }
                    echo "  - 有效主管权限: " . count(array_filter($leader_acl_ids, function ($v) {
                        return $v == '1';
                    })) . " 个" . PHP_EOL;
                } else {
                    echo "  - 主管权限反序列化失败，数据类型: " . gettype($leader_acl_ids) . PHP_EOL;
                }
            } else {
                echo "  - 无主管权限数据" . PHP_EOL;
            }
        }

        echo "合并后权限统计:" . PHP_EOL;
        echo "  - 有效普通权限: " . count($merged_acl_ids) . " 个" . PHP_EOL;
        echo "  - 有效主管权限: " . count($merged_leader_acl_ids) . " 个" . PHP_EOL;

        // 显示具体的权限ID（用于调试）
        if (!empty($merged_acl_ids)) {
            echo "  - 普通权限ID列表: " . implode(', ', array_keys($merged_acl_ids)) . PHP_EOL;
        }
        if (!empty($merged_leader_acl_ids)) {
            echo "  - 主管权限ID列表: " . implode(', ', array_keys($merged_leader_acl_ids)) . PHP_EOL;
        }

        return [
            'acl_ids' => $merged_acl_ids,
            'leader_acl_ids' => $merged_leader_acl_ids
        ];
    }

    /**
     * 将权限合并到目标组织（不覆盖现有权限）
     *
     * @param array $permissions 权限数组
     * @param int $target_org_id 目标组织ID
     * @return array 操作结果
     */
    private function mergePermissionsToOrganize($permissions, $target_org_id)
    {
        try {
            $target_org = Organize::findOne($target_org_id);
            if (!$target_org) {
                return ['success' => false, 'error' => '目标组织不存在'];
            }

            // 备份原有权限（用于日志记录）
            $original_acl_ids = [];
            $original_leader_acl_ids = [];

            if (!empty($target_org->acl_ids)) {
                $original_acl_ids = unserialize($target_org->acl_ids);
                if (!is_array($original_acl_ids)) {
                    $original_acl_ids = [];
                }
            }

            if (!empty($target_org->leader_acl_ids)) {
                $original_leader_acl_ids = unserialize($target_org->leader_acl_ids);
                if (!is_array($original_leader_acl_ids)) {
                    $original_leader_acl_ids = [];
                }
            }

            echo "目标组织原有权限:" . PHP_EOL;
            echo "  - 普通权限: " . count($original_acl_ids) . " 个" . PHP_EOL;
            echo "  - 主管权限: " . count($original_leader_acl_ids) . " 个" . PHP_EOL;

            // 合并权限（保留原有权限，添加新权限）
            $merged_acl_ids = $original_acl_ids;
            $merged_leader_acl_ids = $original_leader_acl_ids;

            // 统计新增权限
            $new_acl_count = 0;
            $new_leader_acl_count = 0;

            // 合并普通权限
            foreach ($permissions['acl_ids'] as $acl_id => $status) {
                if ($status == '1') {
                    if (!isset($merged_acl_ids[$acl_id]) || $merged_acl_ids[$acl_id] != '1') {
                        $new_acl_count++;
                    }
                    $merged_acl_ids[$acl_id] = $status;
                }
            }

            // 合并主管权限
            foreach ($permissions['leader_acl_ids'] as $acl_id => $status) {
                if ($status == '1') {
                    if (!isset($merged_leader_acl_ids[$acl_id]) || $merged_leader_acl_ids[$acl_id] != '1') {
                        $new_leader_acl_count++;
                    }
                    $merged_leader_acl_ids[$acl_id] = $status;
                }
            }

            echo "权限合并统计:" . PHP_EOL;
            echo "  - 合并后普通权限总数: " . count($merged_acl_ids) . " 个" . PHP_EOL;
            echo "  - 合并后主管权限总数: " . count($merged_leader_acl_ids) . " 个" . PHP_EOL;
            echo "  - 新增普通权限: " . $new_acl_count . " 个" . PHP_EOL;
            echo "  - 新增主管权限: " . $new_leader_acl_count . " 个" . PHP_EOL;

            // 显示即将写入的权限数据（用于调试）
            echo "即将写入的权限数据:" . PHP_EOL;
            echo "  - 普通权限序列化数据: " . serialize($merged_acl_ids) . PHP_EOL;
            echo "  - 主管权限序列化数据: " . serialize($merged_leader_acl_ids) . PHP_EOL;

            // 设置合并后的权限
            $target_org->acl_ids = serialize($merged_acl_ids);
            $target_org->leader_acl_ids = serialize($merged_leader_acl_ids);
            $target_org->update_acl_time = time();
            $target_org->update_acl_user_id = 1; // 系统操作用户ID

            if ($target_org->save()) {
                return [
                    'success' => true,
                    'merged_acl_count' => count($merged_acl_ids),
                    'merged_leader_acl_count' => count($merged_leader_acl_ids),
                    'new_acl_count' => $new_acl_count,
                    'new_leader_acl_count' => $new_leader_acl_count,
                    'original_acl_count' => count($original_acl_ids),
                    'original_leader_acl_count' => count($original_leader_acl_ids)
                ];
            } else {
                $errors = [];
                foreach ($target_org->getErrors() as $field => $fieldErrors) {
                    $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                }
                return ['success' => false, 'error' => '保存失败: ' . implode('; ', $errors)];
            }

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 更新用户的组织关系：将用户加入目标组织并移除其他组织关系
     *
     * @param int $user_id 用户ID
     * @param int $target_org_id 目标组织ID
     * @return array 操作结果
     */
    private function updateUserOrganization($user_id, $target_org_id)
    {
        try {
            // 获取用户当前的组织关系
            $current_organizes = \common\models\UserOrganize::find()
                ->where(['user_id' => $user_id])
                ->all();

            echo "用户组织关系更新:" . PHP_EOL;
            echo "  - 当前组织关系数量: " . count($current_organizes) . PHP_EOL;

            // 显示当前所属的组织
            if (!empty($current_organizes)) {
                $current_org_ids = array_column($current_organizes, 'org_id');
                $current_orgs = Organize::find()
                    ->where(['org_id' => $current_org_ids])
                    ->select(['org_id', 'scname'])
                    ->all();

                echo "  - 当前所属组织:" . PHP_EOL;
                foreach ($current_orgs as $org) {
                    echo "    * {$org->scname} (ID: {$org->org_id})" . PHP_EOL;
                }
            }

            // 检查用户是否已经在目标组织中
            $existing_relation = \common\models\UserOrganize::find()
                ->where(['user_id' => $user_id, 'org_id' => $target_org_id])
                ->one();

            $removed_count = 0;

            // 如果用户不在目标组织中，需要先移除所有现有关系，再添加到目标组织
            if (!$existing_relation) {
                // 移除用户的所有组织关系
                $removed_count = \common\models\UserOrganize::deleteAll(['user_id' => $user_id]);
                echo "  - 移除了 {$removed_count} 个组织关系" . PHP_EOL;

                // 将用户添加到目标组织
                $new_relation = new \common\models\UserOrganize();
                $new_relation->user_id = $user_id;
                $new_relation->org_id = $target_org_id;
                $new_relation->is_head = 0; // 默认不是主管

                if ($new_relation->save()) {
                    echo "  - 成功将用户添加到目标组织" . PHP_EOL;
                } else {
                    $errors = [];
                    foreach ($new_relation->getErrors() as $field => $fieldErrors) {
                        $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                    }
                    return ['success' => false, 'error' => '添加用户到目标组织失败: ' . implode('; ', $errors)];
                }
            } else {
                // 用户已在目标组织中，只需移除其他组织关系
                $removed_count = \common\models\UserOrganize::deleteAll([
                    'and',
                    ['user_id' => $user_id],
                    ['!=', 'org_id', $target_org_id]
                ]);
                echo "  - 用户已在目标组织中，移除了其他 {$removed_count} 个组织关系" . PHP_EOL;
            }

            return [
                'success' => true,
                'removed_count' => $removed_count,
                'was_existing' => $existing_relation ? true : false
            ];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 批量将人员移动到特定的组织结构里（不迁移权限）
     *
     * 使用方法:
     * - 用户ID: php yii test/move-users-to-organization 265 201
     * - 批量用户ID: php yii test/move-users-to-organization "265,266,267" 201
     * - 用户姓名: php yii test/move-users-to-organization "张三" 201
     * - 批量用户姓名: php yii test/move-users-to-organization "张三,李四,王五" 201
     * - 混合模式: php yii test/move-users-to-organization "265,张三,267" 201
     *
     * 功能说明:
     * 1. 将指定用户移动到目标组织结构
     * 2. 不迁移任何权限，只改变组织关系
     * 3. 移除用户在其他组织中的关系
     * 4. 支持批量处理多个用户
     * 5. 支持用户ID和姓名混合输入
     * 6. 记录操作日志和统计信息
     *
     * @param string $user_identifiers 用户标识（ID或姓名，单个或逗号分隔的多个）
     * @param int $target_org_id 目标组织结构ID
     * @return void
     */
    public function actionMoveUsersToOrganization($user_identifiers = null, $target_org_id = null)
    {
        // 参数验证
        if (empty($user_identifiers) || empty($target_org_id)) {
            echo "错误: 请提供必要的参数" . PHP_EOL;
            echo "使用方法: php yii test/move-users-to-organization <用户标识> <目标组织ID>" . PHP_EOL;
            echo "示例: " . PHP_EOL;
            echo "  用户ID: php yii test/move-users-to-organization 265 201" . PHP_EOL;
            echo "  批量用户ID: php yii test/move-users-to-organization \"265,266,267\" 201" . PHP_EOL;
            echo "  用户姓名: php yii test/move-users-to-organization \"张三\" 201" . PHP_EOL;
            echo "  批量用户姓名: php yii test/move-users-to-organization \"张三,李四,王五\" 201" . PHP_EOL;
            echo "  混合模式: php yii test/move-users-to-organization \"265,张三,267\" 201" . PHP_EOL;
            return;
        }

        // 解析用户标识列表并转换为用户ID
        $user_identifiers_list = array_map('trim', explode(',', $user_identifiers));
        $user_identifiers_list = array_filter($user_identifiers_list, function ($identifier) {
            return !empty($identifier);
        });

        if (empty($user_identifiers_list)) {
            echo "错误: 没有有效的用户标识" . PHP_EOL;
            return;
        }

        // 将用户标识转换为用户ID
        $user_conversion_result = $this->convertUserIdentifiersToIds($user_identifiers_list);
        if (!$user_conversion_result['success']) {
            echo "错误: " . $user_conversion_result['error'] . PHP_EOL;
            return;
        }

        $user_id_list = $user_conversion_result['user_ids'];
        $conversion_details = $user_conversion_result['details'];

        echo "开始批量移动用户到指定组织..." . PHP_EOL;
        echo "目标组织ID: {$target_org_id}" . PHP_EOL;
        echo str_repeat('-', 50) . PHP_EOL;

        // 显示用户标识转换详情
        echo "用户标识转换结果:" . PHP_EOL;
        foreach ($conversion_details as $detail) {
            if ($detail['type'] == 'id') {
                echo "  - ID {$detail['input']} -> 用户ID: {$detail['user_id']} ({$detail['name']})" . PHP_EOL;
            } else {
                echo "  - 姓名 \"{$detail['input']}\" -> 用户ID: {$detail['user_id']} ({$detail['name']})" . PHP_EOL;
            }
        }
        echo "共找到 " . count($user_id_list) . " 个有效用户" . PHP_EOL;
        echo str_repeat('-', 50) . PHP_EOL;

        // 验证所有用户是否存在（这里应该都存在，因为转换过程中已经验证过）
        $users = \common\models\User::find()
            ->where(['user_id' => $user_id_list])
            ->indexBy('user_id')
            ->all();

        if (count($users) != count($user_id_list)) {
            echo "错误: 用户验证失败，请重试" . PHP_EOL;
            return;
        }

        // 验证目标组织是否存在
        $target_org = Organize::findOne($target_org_id);
        if (!$target_org) {
            echo "错误: 目标组织不存在 (ID: {$target_org_id})" . PHP_EOL;
            return;
        }

        echo "用户列表:" . PHP_EOL;
        foreach ($users as $user) {
            echo "  - {$user->scname} ({$user->username}) [ID: {$user->user_id}]" . PHP_EOL;
        }
        echo "目标组织: {$target_org->scname}" . PHP_EOL;
        echo str_repeat('-', 50) . PHP_EOL;

        // 显示所有用户的当前组织关系
        $all_current_orgs = \common\models\UserOrganize::find()
            ->leftJoin('organize', 'organize.org_id = user_organize.org_id')
            ->leftJoin('user', 'user.user_id = user_organize.user_id')
            ->where(['user_organize.user_id' => $user_id_list])
            ->select(['user_organize.user_id', 'user.scname as user_name', 'organize.org_id', 'organize.scname as org_name'])
            ->asArray()
            ->all();

        if (!empty($all_current_orgs)) {
            echo "用户当前组织关系:" . PHP_EOL;
            $grouped_orgs = [];
            foreach ($all_current_orgs as $org) {
                $grouped_orgs[$org['user_id']][] = $org;
            }

            foreach ($grouped_orgs as $user_id => $orgs) {
                $user_name = $orgs[0]['user_name'];
                echo "  {$user_name} (ID: {$user_id}):" . PHP_EOL;
                foreach ($orgs as $org) {
                    echo "    - {$org['org_name']} (ID: {$org['org_id']})" . PHP_EOL;
                }
            }
        } else {
            echo "所有用户当前都不属于任何组织" . PHP_EOL;
        }

        echo str_repeat('-', 50) . PHP_EOL;
        echo "操作说明:" . PHP_EOL;
        echo "1. 将所有用户移动到目标组织" . PHP_EOL;
        echo "2. 移除用户在其他组织中的关系" . PHP_EOL;
        echo "3. 不迁移任何权限，只改变组织关系" . PHP_EOL;
        echo str_repeat('-', 50) . PHP_EOL;

        // 要求用户确认
        echo "请确认是否继续执行以上操作? (输入 y 确认，其他任意键取消): ";
        $handle = fopen("php://stdin", "r");
        $confirmation = trim(fgets($handle));
        fclose($handle);

        if (strtolower($confirmation) !== 'y') {
            echo "操作已取消。" . PHP_EOL;
            return;
        }

        echo "用户已确认，开始执行批量移动操作..." . PHP_EOL;
        echo str_repeat('=', 60) . PHP_EOL;

        try {
            $processed_users = [];
            $failed_users = [];

            foreach ($user_id_list as $user_id) {
                echo "处理用户 ID: {$user_id} ({$users[$user_id]->scname})" . PHP_EOL;
                echo str_repeat('-', 40) . PHP_EOL;

                try {
                    // 移动用户到目标组织（不迁移权限）
                    $move_result = $this->moveUserToOrganization($user_id, $target_org_id);

                    if ($move_result['success']) {
                        echo "  移动成功 (移除 {$move_result['removed_count']} 个关系)" . PHP_EOL;
                        $processed_users[] = $user_id;
                    } else {
                        echo "  移动失败: " . $move_result['error'] . PHP_EOL;
                        $failed_users[] = $user_id;
                    }

                } catch (\Exception $e) {
                    echo "  处理失败: " . $e->getMessage() . PHP_EOL;
                    $failed_users[] = $user_id;
                }

                echo str_repeat('-', 40) . PHP_EOL;
            }

            // 显示处理结果统计
            echo str_repeat('=', 60) . PHP_EOL;
            echo "批量移动操作完成!" . PHP_EOL;
            echo "处理结果统计:" . PHP_EOL;
            echo "  成功移动用户: " . count($processed_users) . " 个" . PHP_EOL;
            echo "  失败用户: " . count($failed_users) . " 个" . PHP_EOL;
            if (!empty($failed_users)) {
                echo "  失败用户ID: " . implode(', ', $failed_users) . PHP_EOL;
            }
            echo "  操作时间: " . date('Y-m-d H:i:s') . PHP_EOL;

        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . PHP_EOL;
            echo "堆栈跟踪: " . $e->getTraceAsString() . PHP_EOL;
        }
    }

    /**
     * 将用户标识（ID或姓名）转换为用户ID列表
     *
     * @param array $user_identifiers 用户标识列表
     * @return array 转换结果
     */
    private function convertUserIdentifiersToIds($user_identifiers)
    {
        $user_ids = [];
        $details = [];
        $errors = [];
        $duplicate_names = [];

        foreach ($user_identifiers as $identifier) {
            // 判断是否为数字ID
            if (is_numeric($identifier) && $identifier > 0) {
                // 通过ID查找用户
                $user = \common\models\User::find()
                    ->where(['user_id' => $identifier])
                    ->select(['user_id', 'scname'])
                    ->one();

                if ($user) {
                    $user_ids[] = $user->user_id;
                    $details[] = [
                        'type' => 'id',
                        'input' => $identifier,
                        'user_id' => $user->user_id,
                        'name' => $user->scname ?: '未知姓名'
                    ];
                } else {
                    $errors[] = "用户ID {$identifier} 不存在";
                }
            } else {
                // 通过姓名查找用户
                $users = \common\models\User::find()
                    ->where(['scname' => $identifier])
                    ->select(['user_id', 'scname', 'username'])
                    ->all();

                if (empty($users)) {
                    $errors[] = "姓名 \"{$identifier}\" 未找到对应用户";
                } elseif (count($users) == 1) {
                    // 唯一匹配
                    $user = $users[0];
                    $user_ids[] = $user->user_id;
                    $details[] = [
                        'type' => 'name',
                        'input' => $identifier,
                        'user_id' => $user->user_id,
                        'name' => $user->scname,
                        'username' => $user->username
                    ];
                } else {
                    // 多个匹配，记录重名情况
                    $duplicate_info = [];
                    foreach ($users as $user) {
                        $duplicate_info[] = "ID:{$user->user_id}({$user->username})";
                    }
                    $duplicate_names[] = "姓名 \"{$identifier}\" 有多个匹配: " . implode(', ', $duplicate_info);
                }
            }
        }

        // 检查是否有错误
        if (!empty($errors) || !empty($duplicate_names)) {
            $error_message = [];
            if (!empty($errors)) {
                $error_message[] = "查找失败: " . implode('; ', $errors);
            }
            if (!empty($duplicate_names)) {
                $error_message[] = "重名用户: " . implode('; ', $duplicate_names) . "。请使用用户ID或用户名来区分";
            }
            return [
                'success' => false,
                'error' => implode(' | ', $error_message)
            ];
        }

        // 去重用户ID
        $user_ids = array_unique($user_ids);

        return [
            'success' => true,
            'user_ids' => $user_ids,
            'details' => $details
        ];
    }

    /**
     * 将单个用户移动到目标组织（不迁移权限）
     *
     * @param int $user_id 用户ID
     * @param int $target_org_id 目标组织ID
     * @return array 操作结果
     */
    private function moveUserToOrganization($user_id, $target_org_id)
    {
        try {
            // 获取用户当前的组织关系
            $current_organizes = \common\models\UserOrganize::find()
                ->where(['user_id' => $user_id])
                ->all();

            echo "用户组织关系移动:" . PHP_EOL;
            echo "  - 当前组织关系数量: " . count($current_organizes) . PHP_EOL;

            // 显示当前所属的组织
            if (!empty($current_organizes)) {
                $current_org_ids = array_column($current_organizes, 'org_id');
                $current_orgs = Organize::find()
                    ->where(['org_id' => $current_org_ids])
                    ->select(['org_id', 'scname'])
                    ->all();

                echo "  - 当前所属组织:" . PHP_EOL;
                foreach ($current_orgs as $org) {
                    echo "    * {$org->scname} (ID: {$org->org_id})" . PHP_EOL;
                }
            }

            // 检查用户是否已经在目标组织中
            $existing_relation = \common\models\UserOrganize::find()
                ->where(['user_id' => $user_id, 'org_id' => $target_org_id])
                ->one();

            $removed_count = 0;

            // 如果用户不在目标组织中，需要先移除所有现有关系，再添加到目标组织
            if (!$existing_relation) {
                // 移除用户的所有组织关系
                $removed_count = \common\models\UserOrganize::deleteAll(['user_id' => $user_id]);
                echo "  - 移除了 {$removed_count} 个组织关系" . PHP_EOL;

                // 将用户添加到目标组织
                $new_relation = new \common\models\UserOrganize();
                $new_relation->user_id = $user_id;
                $new_relation->org_id = $target_org_id;
                $new_relation->is_head = 0; // 默认不是主管

                if ($new_relation->save()) {
                    echo "  - 成功将用户添加到目标组织" . PHP_EOL;
                } else {
                    $errors = [];
                    foreach ($new_relation->getErrors() as $field => $fieldErrors) {
                        $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                    }
                    return ['success' => false, 'error' => '添加用户到目标组织失败: ' . implode('; ', $errors)];
                }
            } else {
                // 用户已在目标组织中，只需移除其他组织关系
                $removed_count = \common\models\UserOrganize::deleteAll([
                    'and',
                    ['user_id' => $user_id],
                    ['!=', 'org_id', $target_org_id]
                ]);
                echo "  - 用户已在目标组织中，移除了其他 {$removed_count} 个组织关系" . PHP_EOL;
            }

            return [
                'success' => true,
                'removed_count' => $removed_count,
                'was_existing' => $existing_relation ? true : false
            ];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 查找并删除没有任何用户的空组织结构
     *
     * 使用方法: php yii test/clean-empty-organizations
     *
     * 功能说明:
     * 1. 查找所有没有任何用户关联的组织结构
     * 2. 显示这些空组织的详细信息
     * 3. 要求用户确认后执行删除操作
     * 4. 记录删除结果和统计信息
     *
     * @return void
     */
    public function actionCleanEmptyOrganizations()
    {
        echo "开始查找没有任何用户的空组织结构..." . PHP_EOL;
        echo str_repeat('=', 60) . PHP_EOL;

        try {
            // 查找所有没有用户关联的组织结构
            $empty_organizations = $this->findEmptyOrganizations();

            if (empty($empty_organizations)) {
                echo "未找到任何空组织结构。" . PHP_EOL;
                echo "所有组织结构都有用户关联。" . PHP_EOL;
                return;
            }

            echo "找到 " . count($empty_organizations) . " 个空组织结构:" . PHP_EOL;
            echo str_repeat('-', 60) . PHP_EOL;

            // 显示空组织的详细信息
            $this->displayEmptyOrganizations($empty_organizations);

            echo str_repeat('-', 60) . PHP_EOL;
            echo "警告: 删除组织结构是不可逆操作！" . PHP_EOL;
            echo "请确认是否要删除以上 " . count($empty_organizations) . " 个空组织结构?" . PHP_EOL;
            echo "输入 y 确认删除，其他任意键取消: ";

            // 获取用户输入
            $handle = fopen("php://stdin", "r");
            $confirmation = trim(fgets($handle));
            fclose($handle);

            if (strtolower($confirmation) !== 'y') {
                echo "操作已取消。" . PHP_EOL;
                return;
            }

            echo "用户已确认，开始删除空组织结构..." . PHP_EOL;
            echo str_repeat('=', 60) . PHP_EOL;

            // 执行删除操作
            $delete_result = $this->deleteEmptyOrganizations($empty_organizations);

            // 显示删除结果
            echo str_repeat('=', 60) . PHP_EOL;
            echo "删除操作完成!" . PHP_EOL;
            echo "删除结果统计:" . PHP_EOL;
            echo "  - 成功删除: " . $delete_result['success_count'] . " 个组织" . PHP_EOL;
            echo "  - 删除失败: " . $delete_result['failed_count'] . " 个组织" . PHP_EOL;

            if (!empty($delete_result['failed_organizations'])) {
                echo "  - 失败的组织ID: " . implode(', ', array_column($delete_result['failed_organizations'], 'org_id')) . PHP_EOL;
                echo "失败详情:" . PHP_EOL;
                foreach ($delete_result['failed_organizations'] as $failed_org) {
                    echo "    * {$failed_org['scname']} (ID: {$failed_org['org_id']}): {$failed_org['error']}" . PHP_EOL;
                }
            }

            echo "  - 操作时间: " . date('Y-m-d H:i:s') . PHP_EOL;

        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . PHP_EOL;
            echo "堆栈跟踪: " . $e->getTraceAsString() . PHP_EOL;
        }
    }

    /**
     * 查找所有没有用户关联的空组织结构（递归检查子级）
     *
     * @return array 空组织结构列表
     */
    private function findEmptyOrganizations()
    {
        echo "正在查找空组织结构..." . PHP_EOL;

        // 获取所有组织
        $all_organizations = Organize::find()
            ->orderBy('parent_id ASC, orderby ASC')
            ->asArray()
            ->all();

        echo "获取到 " . count($all_organizations) . " 个组织，开始检查..." . PHP_EOL;

        // 获取所有用户组织关系，同时关联用户表和钉钉用户表检查离职状态
        $user_organizations = \common\models\UserOrganize::find()
            ->alias('uo')
            ->leftJoin('user u', 'uo.user_id = u.user_id')
            ->leftJoin('dingtalk_user du', 'u.ding_userid = du.id')
            ->where(['u.leave_office_date' => 0]) // User表中没有离职日期的用户
            ->andWhere([
                'or',
                ['du.delete_time' => null], // 钉钉用户表中没有记录
                ['du.delete_time' => '0000-00-00 00:00:00'] // 钉钉用户表中未离职
            ])
            ->select(['uo.org_id', 'uo.user_id', 'u.scname as user_name'])
            ->asArray()
            ->all();

        echo "获取在职用户组织关系完成，找到 " . count($user_organizations) . " 个在职用户关联" . PHP_EOL;

        // 构建组织在职用户映射
        $org_user_map = [];
        foreach ($user_organizations as $uo) {
            if (!isset($org_user_map[$uo['org_id']])) {
                $org_user_map[$uo['org_id']] = [];
            }
            $org_user_map[$uo['org_id']][] = [
                'user_id' => $uo['user_id'],
                'user_name' => $uo['user_name']
            ];
        }

        echo "构建组织在职用户映射完成，有 " . count($org_user_map) . " 个组织有在职用户关联" . PHP_EOL;

        // 构建组织层级关系
        $org_children_map = [];
        foreach ($all_organizations as $org) {
            if ($org['parent_id'] > 0) {
                if (!isset($org_children_map[$org['parent_id']])) {
                    $org_children_map[$org['parent_id']] = [];
                }
                $org_children_map[$org['parent_id']][] = $org['org_id'];
            }
        }

        echo "构建组织层级关系完成" . PHP_EOL;

        // 检查每个组织是否为空
        $empty_organizations = [];
        foreach ($all_organizations as $org) {
            $is_empty = $this->isOrganizationEmpty($org['org_id'], $org_user_map, $org_children_map);

            if ($is_empty) {
                $empty_organizations[] = $org;
                echo "发现空组织: {$org['scname']} (ID: {$org['org_id']}) - ";

                // 显示为什么是空组织
                $has_active_users = isset($org_user_map[$org['org_id']]) && !empty($org_user_map[$org['org_id']]);
                $has_children = isset($org_children_map[$org['org_id']]) && !empty($org_children_map[$org['org_id']]);

                // 检查是否有离职用户
                $total_users = \common\models\UserOrganize::find()
                    ->where(['org_id' => $org['org_id']])
                    ->count();

                if (!$has_active_users && !$has_children) {
                    if ($total_users > 0) {
                        echo "所有用户已离职且无子级组织 (总用户数: {$total_users})";
                    } else {
                        echo "无用户且无子级组织";
                    }
                } elseif (!$has_active_users && $has_children) {
                    if ($total_users > 0) {
                        echo "所有用户已离职但所有子级组织都为空 (总用户数: {$total_users})";
                    } else {
                        echo "无用户但所有子级组织都为空";
                    }
                }
                echo PHP_EOL;
            }
        }

        echo "检查完成，找到 " . count($empty_organizations) . " 个空组织结构" . PHP_EOL;

        return $empty_organizations;
    }

    /**
     * 检查组织是否为空
     * 空组织的定义：
     * 1. 组织本身没有用户
     * 2. 要么没有子级组织，要么所有子级组织都是空的
     *
     * @param int $org_id 组织ID
     * @param array $org_user_map 组织用户映射
     * @param array $org_children_map 组织子级映射
     * @return bool 是否为空组织
     */
    private function isOrganizationEmpty($org_id, $org_user_map, $org_children_map)
    {
        // 1. 检查当前组织是否有用户
        if (isset($org_user_map[$org_id]) && !empty($org_user_map[$org_id])) {
            return false; // 当前组织有用户，不是空组织
        }

        // 2. 检查子级组织
        if (isset($org_children_map[$org_id]) && !empty($org_children_map[$org_id])) {
            // 有子级组织，需要检查所有子级是否都为空
            foreach ($org_children_map[$org_id] as $child_org_id) {
                // 如果任何一个子级组织不为空，则当前组织不算空
                if (!$this->isOrganizationEmpty($child_org_id, $org_user_map, $org_children_map)) {
                    return false;
                }
            }
            // 所有子级组织都为空，当前组织也算空
            return true;
        } else {
            // 没有子级组织，且前面已确认没有用户，所以是空组织
            return true;
        }
    }

    /**
     * 显示空组织结构的详细信息
     *
     * @param array $empty_organizations 空组织结构列表
     */
    private function displayEmptyOrganizations($empty_organizations)
    {
        // 获取所有组织的父级信息，用于显示层级关系
        $all_organizations = Organize::find()
            ->select(['org_id', 'scname', 'parent_id'])
            ->indexBy('org_id')
            ->asArray()
            ->all();

        echo sprintf("%-8s %-30s %-30s %-10s %-12s", "组织ID", "组织名称", "父级组织", "状态", "创建时间") . PHP_EOL;
        echo str_repeat('-', 90) . PHP_EOL;

        foreach ($empty_organizations as $org) {
            // 获取父级组织名称
            $parent_name = '无';
            if ($org['parent_id'] > 0 && isset($all_organizations[$org['parent_id']])) {
                $parent_name = $all_organizations[$org['parent_id']]['scname'];
            }

            // 状态显示
            $status_text = $org['status'] == 1 ? '启用' : '禁用';

            // 创建时间
            $create_time = $org['itime'] ? date('Y-m-d', $org['itime']) : '未知';

            echo sprintf(
                "%-8s %-30s %-30s %-10s %-12s",
                $org['org_id'],
                mb_substr($org['scname'], 0, 12) . (mb_strlen($org['scname']) > 12 ? '...' : ''),
                mb_substr($parent_name, 0, 12) . (mb_strlen($parent_name) > 12 ? '...' : ''),
                $status_text,
                $create_time
            ) . PHP_EOL;
        }
    }

    /**
     * 删除空组织结构
     *
     * @param array $empty_organizations 要删除的空组织结构列表
     * @return array 删除结果统计
     */
    private function deleteEmptyOrganizations($empty_organizations)
    {
        $success_count = 0;
        $failed_count = 0;
        $failed_organizations = [];

        foreach ($empty_organizations as $org_data) {
            try {
                echo "正在删除组织: {$org_data['scname']} (ID: {$org_data['org_id']})...";

                // 查找组织对象
                $organization = Organize::findOne($org_data['org_id']);
                if (!$organization) {
                    echo " 失败 - 组织不存在" . PHP_EOL;
                    $failed_count++;
                    $failed_organizations[] = [
                        'org_id' => $org_data['org_id'],
                        'scname' => $org_data['scname'],
                        'error' => '组织不存在'
                    ];
                    continue;
                }

                // 再次确认该组织确实没有在职用户（安全检查）
                $active_user_count = \common\models\UserOrganize::find()
                    ->alias('uo')
                    ->leftJoin('user u', 'uo.user_id = u.user_id')
                    ->leftJoin('dingtalk_user du', 'u.ding_userid = du.id')
                    ->where(['uo.org_id' => $org_data['org_id']])
                    ->andWhere(['u.leave_office_date' => 0]) // User表中没有离职日期的用户
                    ->andWhere([
                        'or',
                        ['du.delete_time' => null], // 钉钉用户表中没有记录
                        ['du.delete_time' => '0000-00-00 00:00:00'] // 钉钉用户表中未离职
                    ])
                    ->count();

                if ($active_user_count > 0) {
                    echo " 跳过 - 发现有 {$active_user_count} 个在职用户" . PHP_EOL;
                    $failed_count++;
                    $failed_organizations[] = [
                        'org_id' => $org_data['org_id'],
                        'scname' => $org_data['scname'],
                        'error' => "发现有 {$active_user_count} 个在职用户，跳过删除"
                    ];
                    continue;
                }

                // 再次递归检查子级组织是否有用户（安全检查）
                $has_users_in_children = $this->hasUsersInChildren($org_data['org_id']);
                if ($has_users_in_children) {
                    echo " 跳过 - 子级组织中发现有用户" . PHP_EOL;
                    $failed_count++;
                    $failed_organizations[] = [
                        'org_id' => $org_data['org_id'],
                        'scname' => $org_data['scname'],
                        'error' => "子级组织中发现有用户，跳过删除"
                    ];
                    continue;
                }

                // 先获取并显示相关的 user_organize 记录详情（包括离职人员的记录）
                $user_organize_records = \common\models\UserOrganize::find()
                    ->alias('uo')
                    ->leftJoin('user u', 'uo.user_id = u.user_id')
                    ->leftJoin('dingtalk_user du', 'u.ding_userid = du.id')
                    ->where(['uo.org_id' => $org_data['org_id']])
                    ->select([
                        'uo.user_id',
                        'u.scname as user_name',
                        'u.leave_office_date',
                        'du.delete_time'
                    ])
                    ->asArray()
                    ->all();

                if (!empty($user_organize_records)) {
                    echo " 清理 " . count($user_organize_records) . " 条用户组织关系记录";

                    // 统计在职和离职用户
                    $active_users = [];
                    $inactive_users = [];

                    foreach ($user_organize_records as $record) {
                        $is_active = ($record['leave_office_date'] == 0) &&
                            (empty($record['delete_time']) || $record['delete_time'] == '0000-00-00 00:00:00');

                        if ($is_active) {
                            $active_users[] = $record['user_name'] ?: "用户ID:{$record['user_id']}";
                        } else {
                            $inactive_users[] = $record['user_name'] ?: "用户ID:{$record['user_id']}";
                        }
                    }

                    if (!empty($inactive_users)) {
                        echo " (离职用户: " . implode(', ', $inactive_users) . ")";
                    }
                    if (!empty($active_users)) {
                        echo " (在职用户: " . implode(', ', $active_users) . ")";
                    }

                    // 删除所有用户组织关系记录
                    $deleted_relations = \common\models\UserOrganize::deleteAll(['org_id' => $org_data['org_id']]);
                    echo " 已清理完成";
                }

                // 执行删除组织
                if ($organization->delete()) {
                    echo " 成功" . PHP_EOL;
                    $success_count++;
                } else {
                    $errors = [];
                    foreach ($organization->getErrors() as $field => $fieldErrors) {
                        $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                    }
                    $error_message = implode('; ', $errors);
                    echo " 失败 - " . $error_message . PHP_EOL;
                    $failed_count++;
                    $failed_organizations[] = [
                        'org_id' => $org_data['org_id'],
                        'scname' => $org_data['scname'],
                        'error' => $error_message
                    ];
                }

            } catch (\Exception $e) {
                echo " 失败 - " . $e->getMessage() . PHP_EOL;
                $failed_count++;
                $failed_organizations[] = [
                    'org_id' => $org_data['org_id'],
                    'scname' => $org_data['scname'],
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'success_count' => $success_count,
            'failed_count' => $failed_count,
            'failed_organizations' => $failed_organizations
        ];
    }

    /**
     * 递归检查组织的所有子级是否有在职用户
     *
     * @param int $org_id 组织ID
     * @return bool 子级组织中是否有在职用户
     */
    private function hasUsersInChildren($org_id)
    {
        // 获取直接子级组织
        $child_organizations = Organize::find()
            ->where(['parent_id' => $org_id])
            ->select(['org_id'])
            ->asArray()
            ->all();

        if (empty($child_organizations)) {
            return false; // 没有子级组织
        }

        foreach ($child_organizations as $child_org) {
            // 检查子级组织是否有在职用户
            $active_user_count = \common\models\UserOrganize::find()
                ->alias('uo')
                ->leftJoin('user u', 'uo.user_id = u.user_id')
                ->leftJoin('dingtalk_user du', 'u.ding_userid = du.id')
                ->where(['uo.org_id' => $child_org['org_id']])
                ->andWhere(['u.leave_office_date' => 0]) // User表中没有离职日期的用户
                ->andWhere([
                    'or',
                    ['du.delete_time' => null], // 钉钉用户表中没有记录
                    ['du.delete_time' => '0000-00-00 00:00:00'] // 钉钉用户表中未离职
                ])
                ->count();

            if ($active_user_count > 0) {
                return true; // 子级组织有在职用户
            }

            // 递归检查子级的子级
            if ($this->hasUsersInChildren($child_org['org_id'])) {
                return true; // 子级的子级有在职用户
            }
        }

        return false; // 所有子级组织都没有在职用户
    }



    /**
     * 测试word
     * php yii test/test-word
     */
    public function actionTestWord()
    {
        // 指定订货合同模板文件路径
        $file = __DIR__ . '/../../app_admin/files/templates/订货合同模板.docx';

        // 检查文件是否存在
        if (!file_exists($file)) {
            echo "文件不存在: {$file}" . PHP_EOL;
            return;
        }

        $output = __DIR__ . '/../../app_admin/files/templates/订货合同模板2.docx';
        DocxProcessor::quickProcess(
            $file,
            [
                'name' => '张三',
            ]
            ,
            $output,
            "123456"
        );

        echo "成功加载文件: {$file}" . PHP_EOL;
    }

    /**
     * 测试word格式对比
     * php yii test/test-word-compare
     */
    public function actionTestWordCompare()
    {
        // 指定订货合同模板文件路径
        $file = __DIR__ . '/../../app_admin/files/templates/订货合同模板.docx';

        // 检查文件是否存在
        if (!file_exists($file)) {
            echo "文件不存在: {$file}" . PHP_EOL;
            return;
        }

        // 生成不带密码保护的文档
        $outputNormal = __DIR__ . '/../../app_admin/files/templates/订货合同模板_无保护.docx';
        DocxProcessor::quickProcess(
            $file,
            [
                'name' => '张三',
            ],
            $outputNormal
        );

        // 生成带密码保护的文档
        $outputProtected = __DIR__ . '/../../app_admin/files/templates/订货合同模板_有保护.docx';
        DocxProcessor::quickProcess(
            $file,
            [
                'name' => '张三',
            ],
            $outputProtected,
            "123456"
        );

        echo "成功生成对比文档:" . PHP_EOL;
        echo "  - 无保护文档: {$outputNormal}" . PHP_EOL;
        echo "  - 有保护文档: {$outputProtected}" . PHP_EOL;

        // 检查文件大小
        if (file_exists($outputNormal) && file_exists($outputProtected)) {
            $sizeNormal = filesize($outputNormal);
            $sizeProtected = filesize($outputProtected);
            echo "文件大小对比:" . PHP_EOL;
            echo "  - 无保护文档: {$sizeNormal} 字节" . PHP_EOL;
            echo "  - 有保护文档: {$sizeProtected} 字节" . PHP_EOL;

            $sizeDiff = abs($sizeNormal - $sizeProtected);
            $sizePercent = round(($sizeDiff / $sizeNormal) * 100, 2);
            echo "  - 大小差异: {$sizeDiff} 字节 ({$sizePercent}%)" . PHP_EOL;

            if ($sizePercent < 5) {
                echo "✓ 文档格式保持良好，大小差异在合理范围内" . PHP_EOL;
            } else {
                echo "⚠ 文档大小差异较大，可能存在格式变化" . PHP_EOL;
            }
        }
    }

    /**
     * 测试天猫超市退款后销售额汇总功能
     * 使用方法: php yii test/tmall-supermarket-refund-summary
     */
    public function actionTmallSupermarketRefundSummary()
    {
        echo "开始测试天猫超市退款后销售额汇总功能..." . PHP_EOL;

        try {
            // 首先测试 MongoDB 连接和数据
            echo "1. 测试 MongoDB 连接..." . PHP_EOL;
            $tmallData = \common\models\mongo\TmallSupermarketRefundData::find()->limit(5)->all();
            echo "   MongoDB 连接成功，找到 " . count($tmallData) . " 条测试数据" . PHP_EOL;

            if (count($tmallData) > 0) {
                $sample = $tmallData[0];
                echo "   样本数据 - 店铺ID: " . ($sample['shopId'] ?? '未知') .
                    ", 店铺名: " . ($sample['shopName'] ?? '未知') .
                    ", 统计日期: " . ($sample['stat_date'] ?? '未知') . PHP_EOL;
            }

            // 查询最近的数据
            echo "2. 查询最近的天猫超市数据..." . PHP_EOL;
            $recentData = \common\models\mongo\TmallSupermarketRefundData::find()
                ->orderBy(['date' => SORT_DESC])
                ->limit(1)
                ->one();

            if ($recentData) {
                $latestDate = $recentData['stat_date'] ?? '';
                echo "   最新数据日期: " . $latestDate . PHP_EOL;

                // 使用最新数据的月份进行测试
                if (!empty($latestDate) && strlen($latestDate) >= 6) {
                    $testMonth = substr($latestDate, 0, 4) . '-' . substr($latestDate, 4, 2);
                    echo "   使用测试月份: " . $testMonth . PHP_EOL;
                } else {
                    $testMonth = date('Y-m');
                    echo "   使用当前月份: " . $testMonth . PHP_EOL;
                }
            } else {
                $testMonth = date('Y-m');
                echo "   未找到数据，使用当前月份: " . $testMonth . PHP_EOL;
            }

            echo "3. 测试业务服务集成..." . PHP_EOL;
            $businessService = new BusinessService();

            // 设置测试参数 - 添加必要的过滤条件
            $where = [
                'sdate' => $testMonth . '-01',
                'edate' => date('Y-m-t', strtotime($testMonth . '-01')),
                'select_date' => $testMonth,
                'auth' => true,
                'directSalesType' => '3', // 全部类型
            ];

            // 获取日期数组
            list($date) = $businessService->getPerformanceTableHeader(false, true, $where);

            echo "   查询时间范围: " . $where['sdate'] . " 到 " . $where['edate'] . PHP_EOL;

            // 先测试 SQL 查询部分
            $reflection = new \ReflectionClass($businessService);
            $sqlMethod = $reflection->getMethod('getPerformanceDaysCountSql');
            $sqlMethod->setAccessible(true);
            $sqlResult = $sqlMethod->invoke($businessService, $where, $date, -1);

            $sqlData = \Yii::$app->db->createCommand($sqlResult['sql'], $sqlResult['bind'])->queryAll();
            echo "   SQL 查询返回 " . count($sqlData) . " 条基础数据" . PHP_EOL;

            // 调用天猫超市数据汇总功能 - 使用管理员用户ID
            $result = $businessService->getPerformanceDaysCount($where, $date, -1);

            // 检查结果中是否包含天猫超市数据
            $tmallSupermarketCount = 0;
            foreach ($result['mallTotal'] as $mall) {
                if (isset($mall['mallPlatform']) && $mall['mallPlatform'] === '天猫超市') {
                    $tmallSupermarketCount++;
                    echo "   发现天猫超市店铺: " . ($mall['mallName'] ?? '未知') .
                        " (ID: " . ($mall['mallId'] ?? '未知') . ")" .
                        " 总销售额: " . ($mall['total'] ?? 0) .
                        " 退款金额: " . ($mall['returnAmount'] ?? 0) . PHP_EOL;
                }
            }

            if ($tmallSupermarketCount > 0) {
                echo "✓ 成功集成了 {$tmallSupermarketCount} 个天猫超市店铺的数据" . PHP_EOL;
            } else {
                echo "⚠ 未发现天猫超市数据在业务汇总中" . PHP_EOL;
            }

            echo "   总店铺数量: " . count($result['mallTotal']) . PHP_EOL;
            echo "测试完成！" . PHP_EOL;

        } catch (\Exception $e) {
            echo "测试失败: " . $e->getMessage() . PHP_EOL;
            echo "错误堆栈: " . $e->getTraceAsString() . PHP_EOL;
        }
    }

    /**
     * 直接测试天猫超市数据集成方法
     * 使用方法: php yii test/tmall-supermarket-direct
     */
    public function actionTmallSupermarketDirect()
    {
        echo "开始直接测试天猫超市数据集成..." . PHP_EOL;

        try {
            // 查询最近的天猫超市数据
            $recentData = \common\models\mongo\TmallSupermarketRefundData::find()
                ->orderBy(['date' => SORT_DESC])
                ->limit(10)
                ->all();

            if (empty($recentData)) {
                echo "未找到天猫超市数据" . PHP_EOL;
                return;
            }

            echo "找到 " . count($recentData) . " 条最新数据" . PHP_EOL;

            // 获取最新数据的日期范围
            $latestDate = $recentData[0]['stat_date'] ?? '';
            if (empty($latestDate) || strlen($latestDate) < 8) {
                echo "数据日期格式错误: " . $latestDate . PHP_EOL;
                return;
            }

            $testYear = substr($latestDate, 0, 4);
            $testMonth = substr($latestDate, 4, 2);
            $testDay = substr($latestDate, 6, 2);
            $testDate = $testYear . '-' . $testMonth . '-' . $testDay;

            echo "使用测试日期: " . $testDate . PHP_EOL;

            // 创建测试参数
            $where = [
                'sdate' => $testYear . '-' . $testMonth . '-01',
                'edate' => $testYear . '-' . $testMonth . '-31',
                'select_date' => $testYear . '-' . $testMonth,
            ];

            $date = [$testDate]; // 只测试一天

            echo "测试参数: " . json_encode($where) . PHP_EOL;
            echo "测试日期数组: " . json_encode($date) . PHP_EOL;

            // 直接调用集成方法
            $businessService = new BusinessService();
            $reflection = new \ReflectionClass($businessService);
            $method = $reflection->getMethod('integrateTmallSupermarketData');
            $method->setAccessible(true);

            $mallTotal = []; // 空的初始数据
            $result = $method->invoke($businessService, $mallTotal, $where, $date);

            echo "集成结果: 找到 " . count($result) . " 个店铺数据" . PHP_EOL;

            foreach ($result as $shop) {
                if (isset($shop['mallPlatform']) && $shop['mallPlatform'] === '天猫超市') {
                    echo "天猫超市店铺: " . ($shop['mallName'] ?? '未知') .
                        " (ID: " . ($shop['mallId'] ?? '未知') . ")" .
                        " 总销售额: " . ($shop['total'] ?? 0) .
                        " 退款金额: " . ($shop['returnAmount'] ?? 0) . PHP_EOL;

                    // 显示每日数据
                    foreach ($date as $day) {
                        if (isset($shop[$day])) {
                            echo "  {$day}: 销售额 " . $shop[$day] . ", 退款 " . ($shop['return_' . $day] ?? 0) . PHP_EOL;
                        }
                    }
                }
            }

            echo "测试完成！" . PHP_EOL;

        } catch (\Exception $e) {
            echo "测试失败: " . $e->getMessage() . PHP_EOL;
            echo "错误堆栈: " . $e->getTraceAsString() . PHP_EOL;
        }
    }
}
