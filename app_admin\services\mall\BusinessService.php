<?php

namespace app_admin\services\mall;

use common\models\Brand;
use common\models\mongo\TmallSupermarketRefundData;
use common\models\User;
use Yii;
use yii\db\Query;

class BusinessService
{
    private $departmentSort = [
        '电商一部' => [],
        '电商二部' => [],
        '电商三部' => [],
        '创新事业部' => [],
        '广州事业部' => [],
        '其他部门' => []
    ];

    private $mallGroupSort = [
        '一部整体' => [],
        '护肤一组' => [],
        '护肤二组' => [],
        '护肤三组' => [],
        '护肤四组' => [],
        '彩妆五组' => [],
        '三部整体' => [],
        '店播组' => [],
        '达播组' => [],
        '私域组' => [],
        '喵汪侠项目组' => [],
        '创新飘影组' => [],
        '经销1组' => [],
        '飘影项目组' => [],
        '孔凤春项目组' => [],
        '经销2组' => [],
        '经销3组' => [],
        '加工' => [],
        '其他组' => []
    ];

    public function __construct()
    {
        $sql = "select caiwuDept,ordr from (select caiwuDept,ordr FROM pdd_mall where enable=1 ORDER BY ordr asc) m GROUP BY m.caiwuDept ORDER BY ordr ASC";
        $caiwuDept = Yii::$app->db->createCommand($sql)->queryAll();
        $departmentSort = [];
        foreach ($caiwuDept as $value) {
            $d = $value['caiwuDept'] ?? '';
            if (!empty($d)) {
                $departmentSort[$d] = [];
            }
        }
        $departmentSort['其他部门'] = [];
        $this->departmentSort = $departmentSort;
    }

    public function getPerformanceTableHeader($isYear, $isCurrent, $where)
    {
        $select_date = $where['select_date'];
        // 获取表格头部
        // $header = ["已完成%", "已完成", "本月目标GMV", "部门", "渠道", "主体", "店铺名称"];
        // if ($isYear) {
        //     $header = ["已完成%", "年目标GMV", "按部门/渠道/主体", "已完成"];
        // }

        $date = [];
        if ($isYear) {
            // $select_date 2025
            $y = (int) date("Y");
            if ((int) $select_date < $y) {
                $d = 12;
            } else {
                $d = (int) date("m");
            }
            for ($i = 1; $i <= $d; ++$i) {
                if ($i <= 9) {
                    $date[] = $select_date . '-0' . $i;
                } else {
                    $date[] = $select_date . '-' . $i;
                }
            }
            if ($select_date == date('Y')) {
                $date = array_reverse($date);
            }
        } else {
            // $select_date 2025-01
            // 选择月份的第一天
            $sdate = date('Y-m-d', strtotime($select_date));
            $isCurrent = $sdate == date('Y-m-01');

            if (!$isCurrent) {
                // 选择月份的最后一天
                $day = (int) date('d', strtotime('+1 month -1 day', strtotime($sdate)));
            } else if ($isCurrent && (int) date('d') != 1) {
                // 选择昨天 
                $day = (int) date('d', strtotime('yesterday'));
            } else {
                $day = 1;
            }

            for ($i = 1; $i <= $day; ++$i) {
                if ($i <= 9) {
                    $date[] = $select_date . '-0' . $i;
                } else {
                    $date[] = $select_date . '-' . $i;
                }
            }
            if ($select_date == date('Y-m')) {
                $date = array_reverse($date);
            }
        }

        return [$date];
        // return [$header, $date];
    }

    public function getPerformanceMonthsCount($where, $date, $uid)
    {
        // 店铺汇总
        $res = $this->getPerformanceMonthsCountSql($where, $date, $uid);
        // ->getRawSql();  ->queryAll();
        $mallTotal = Yii::$app->db->createCommand($res['sql'], $res['bind'])->queryAll();

        $mallTotal = $this->mergeMallData($mallTotal);
        // 直营的过滤条件通过pdd_mall_indirect_sales_group_config配置进行代码过滤
        $mallTotal = $this->getPerformanceDisposalData($mallTotal, $where);
        // 全部、直营、非直营 的过滤条件
        $mallTotal = $this->directSalesTypeWhere($mallTotal, $where);

        // 集成天猫超市数据
        $mallTotal = $this->integrateTmallSupermarketData($mallTotal, $where, $date);

        // 部门汇总 department
        $array = $this->getPerformanceDepCount($mallTotal, $date);
        $mallDepTotal = $array['data'];
        // 渠道汇总 platform
        $array = $this->getPerformancePlatformCount($mallTotal, $date);
        $mallPlatformTotal = $array['data'];
        // 组别汇总 mallGroup  getPerformanceMallGroupCount
        $array = $this->getPerformanceMallGroupCount($mallTotal, $date);
        $mallGroupTotal = $array['data'];
        // 主体汇总 mallCompany
        $array = $this->getPerformanceMallCompanyCount($mallTotal, $date);
        $mallCompanyTotal = $array['data'];

        // 全部汇总
        $array = $this->getPerformanceAllCount($mallTotal, $date);
        $mallAllTotal = $array['data'];

        // 最终汇总(全部汇总只统计展示的目标销售-没有展示全年的目标销售)
        $array = $this->getPerformanceAllTotal($where, $mallTotal, $mallAllTotal);
        $allTotal = $array['allTotal'];

        return [
            'mallTotal' => $mallTotal,
            'mallDepTotal' => $mallDepTotal,
            'mallPlatformTotal' => $mallPlatformTotal,
            'mallGroupTotal' => $mallGroupTotal,
            'mallCompanyTotal' => $mallCompanyTotal,
            'mallAllTotal' => $mallAllTotal,
            'allTotal' => $allTotal
        ];
    }

    /**
     * 获取店铺的统计sql
     * 删除了排序 caiwuOrdr、caiwuDeptOrdr、mallCompanyOrdr、mallPlatformOrdr
     */
    public function getPerformanceMonthsCountSql($where, $date, $uid)
    {
        $sdate = $where['sdate'];
        $edate = date('Y-m-d', strtotime($where['edate']) + 86400);
        $year = $where['select_date'];

        $select1 = "";
        $select2 = "";
        $select3 = "";
        $select4 = "";
        // business_goal_data_record date_month 业务目标数据表的查询条件
        // $selectz = [];
        foreach ($date as $yearMonths) {
            // $yearMonths   YYYY-MM
            $select1 .= "MAX(CASE WHEN a.yearMonth = '{$yearMonths}' THEN a.total END) AS `$yearMonths`,";
            $select2 .= "MAX(CASE WHEN a.yearMonth = '{$yearMonths}' THEN a.total END) AS `$yearMonths`,";
            $select3 .= "MAX(CASE WHEN a.yearMonth = '{$yearMonths}' THEN a.gmv END) AS `$yearMonths`,";
            $select4 .= "MAX(CASE WHEN a.yearMonth = '" . date("Y年n月", strtotime($yearMonths)) . "' THEN a.gmv END) AS `$yearMonths`,";
            // 不补0的月份
            // $selectz[] = "'" . date("n", strtotime($yearMonths)) . "'";
        }
        // $selectz = implode(',', $selectz);

        $bind = [
            ':sdate' => $sdate,
            ':edate' => $edate,
            ':year' => $year
        ];
        // 拼多多(pdd_jysj pdd_fwsj pdd_mall)
        $pdd_sql = "SELECT 
                a.caiwuDept caiwuDept,
                a.mallCompany,
                a.mallPlatform platform,
                a.mallId,a.mallName,
                {$select1}
                SUM(total) total 
            FROM(
	            SELECT 
                    CONCAT(YEAR(a.statDate),'-',LPAD(MONTH(a.statDate),2,'0')) yearMonth,
                    a.caiwuDept,
                    a.mallCompany,
                    a.mallPlatform,
                    a.mallId,
                    a.mallName,
                    SUM(a.total_amount) total 
                FROM(
		            SELECT 
                        a.mallId,
                        m.mallName,
                        m.caiwuDept,
                        m.mallCompany,
                        m.mallPlatform,
                        a.statDate,
                        a.amount jysj_amount,
                        b.amount fwsj_amount,
                        a.amount-b.amount total_amount 
                    FROM (
				        SELECT mallId,statDate,SUM(payOrdrAmt) amount FROM pdd_jysj
				        WHERE 
                            statDate>=:sdate AND statDate<:edate
				        GROUP BY mallId,statDate
		            ) a
		            JOIN (
				        SELECT mallId,statDate,SUM(sucRfOrdrAmt1d) amount FROM pdd_fwsj
                        WHERE statDate>=:sdate AND statDate<:edate
				        GROUP BY mallId,statDate
		            ) b 
                    ON a.mallId=b.mallId AND a.statDate=b.statDate 
		            JOIN pdd_mall m ON a.mallId=m.mallId AND m.mallPlatform='拼多多' AND m.mallStatus=1
	            )a
	            GROUP BY 
                    YEAR(a.statDate),
                    MONTH(a.statDate),
                    a.caiwuDept,
                    a.mallCompany,
                    a.mallPlatform,
                    a.mallId,
                    a.mallName
            )a
            GROUP BY 
                a.caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.mallId,
                a.mallName";

        // -------------------------------------------------------------------------------------------
        // 管易 guanyierp_shop  guanyierp_trade_deliverys_2(发货单) guanyierp_trade_deliverys_details
        $vs_mall_data_sql = "SELECT 
                CASE WHEN a.caiwuDept = '其他' OR a.caiwuDept = '销售四部' THEN '创新事业部' ELSE a.caiwuDept END AS caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.mallId,
                a.mallName,
                {$select2}
                SUM(a.total) total 
            FROM(
                SELECT 
                    CONCAT(YEAR(a.statDate),'-',LPAD(MONTH(a.statDate),2,'0')) yearMonth,
                    a.caiwuDept,
                    a.mallCompany,
                    a.mallPlatform,
                    a.shop_code mallId,
                    a.shop_name mallName,
                    SUM(total) total  
                FROM guanyierp_trade_deliverys_32 a 
                WHERE a.statDate>=:sdate and a.statDate<:edate 
                GROUP BY 
                    a.caiwuDept,
                    a.mallCompany,
                    a.mallPlatform,
                    a.shop_code,
                    a.shop_name,
                    yearMonth
            )a
            GROUP BY 
                a.caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.mallId,
                a.mallName";

        // pdd_mall vs_mall_data(视图) '快手', '小红书', '京东', '淘系', '天猫', '抖音', '微信'
        $guanyi_sql = "SELECT 
                a.caiwuDept caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.mallId,
                a.mallName,
                $select3
                SUM(a.gmv) total 
            FROM(
                SELECT 
                    a.caiwuDept,
                    a.mallCompany,
                    a.mallPlatform,
                    a.mallId,
                    a.mallName,
                    CONCAT(YEAR(b.date),'-',LPAD(MONTH(b.date),2,'0'),'') yearMonth,
                    SUM(b.gmv) gmv 
                FROM pdd_mall a 
                LEFT JOIN vs_mall_data b ON a.mallId=b.mallId AND b.date>=:sdate AND b.date<:edate  
                WHERE a.mallStatus=1 AND a.mallPlatform IN ('快手','小红书','京东','淘系','天猫','抖音','微信') 
                GROUP BY 
                    a.caiwuDept,
                    a.mallCompany,
                    a.mallPlatform,
                    a.mallId,
                    a.mallName,
                    YEAR(b.date),
                    MONTH(b.date) 
            )a
            GROUP BY 
                a.caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.mallId,
                a.mallName";

        // 销售四部、guanyierp_trade_deliverys_32、pdd_mall...
        $d4_sql = "SELECT 
                CASE WHEN a.caiwuDept = '其他' OR a.caiwuDept = '销售四部' THEN '创新事业部' ELSE a.caiwuDept END AS caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.mallId,
                a.mallName,
                $select4
                SUM(a.gmv) total 
            FROM(
                SELECT 
                    IFNULL(mall.caiwuDept,a.caiwuDept) caiwuDept,
                    IFNULL(mall.mallCompany,a.mallCompany) mallCompany,
                    IFNULL(mall.mallPlatform,a.mallPlatform) mallPlatform,
                    IFNULL(CASE WHEN mall.mallId = '*********' THEN '05.02.015' ELSE mall.mallId END,a.mallId) mallId,
                    a.mallName,
                    a.yearMonth,
                    sum(c.amount_after) gmv 
                FROM(
                    select 
                        a.type_name caiwuDept,
                        a.mallCompany,
                        a.mallPlatform,
                        a.code mallId,
                        a.name mallName,
                        YEAR(b.dayTime) ayear,
                        MONTH(b.dayTime) amonth,
                        b.yearMonth2 yearMonth 
                    from guanyierp_shop a 
                    cross join yearMonth b on dayTime>=:sdate and dayTime<:edate  
                    where is_gmv_gz_jxs=1 
                )a
                LEFT JOIN guanyierp_trade_deliverys_2 b 
                    ON a.mallId=b.shop_code 
                    AND a.ayear=YEAR(b.delivery_date) 
                    AND a.amonth=MONTH(b.delivery_date) 
                    AND b.delivery_date>=:sdate 
                    AND b.delivery_date<:edate 
                LEFT JOIN guanyierp_trade_deliverys_details c 
                ON b.id=c.pid 
                LEFT JOIN pdd_mall mall ON a.mallId=mall.gyCode
                GROUP BY 
                    a.caiwuDept,
                    a.mallCompany,
                    a.mallPlatform,
                    a.mallId,
                    a.mallName,
                    a.yearMonth 
                )a
            GROUP BY 
                a.caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.mallId,
                a.mallName";


        $noMallGroupDisposeSql = $this->noMallGroupDisposeSql($year);
        // 开始拼接sql
        $sql = "SELECT a.*,b.mallPlatform as platform, b.mallCompany as mallCompany,b.caiwuDept department,b.mallGroup,b.createTime as createTime,b.gyCode,b.dest,IFNULL(b.ordr,999999) as `order`,b.enable as `enable`,b.mallStatus as mallStatus,ROUND(a.total*100/b.dest,2) progress FROM( 
                {$pdd_sql} 
                UNION 
                {$vs_mall_data_sql} 
                UNION 
                {$guanyi_sql} 
                UNION 
                {$d4_sql} 
            )a LEFT JOIN ( 
                SELECT 
                    m.mallPlatform,
                    m.mallCompany,
                    m.caiwuDept,
                    m.mallId,
                    m.mallName,
                    m.gyName,
                    m.gyCode,
                    m.mallGroup,
                    m.ordr,
                    m.enable,
                    m.mallStatus,
                    m.brand,
                    SUM(bap.shop_sales_volume_goal) dest,
                    m.createTime 
                FROM pdd_mall m 
                LEFT JOIN business_annual_planning bap 
                ON m.gyCode=bap.shop_code AND bap.year=:year 
                WHERE m.mallStatus=1  
                -- AND m.mallPlatform IN('拼多多','快手','小红书','京东','淘系','天猫','抖音','微信') 
                GROUP BY m.mallId,m.mallName,m.gyName,m.gyCode 
                UNION 
                {$noMallGroupDisposeSql}
            )b 
            ON a.mallId=b.mallId";
        // ON m.gyCode=bap.shop_code AND bap.year=:year AND bap.month IN($selectz)

        if ($where['auth'] === true) {
            // (超管+负责人)、运营人员
            $sql .= " where 1 = 1  and (b.createTime < :edate or a.total > 0) ";
        } else {
            // 没有权限的直接返回空数组
            $sql .= " where 1 != 1 ";
        }

        if (isset($where['authGyCodeIn']) && count($where['authGyCodeIn']) >= 1) {
            // 运营人员 查看指定店铺的条件
            foreach ($where['authGyCodeIn'] as $key => &$authGyCodeIn) {
                // $authGyCodeIn = "'{$authGyCodeIn}'";
                $bind[':b_gyCode_' . $key] = $authGyCodeIn;
                $authGyCodeIn = ':b_gyCode_' . $key;
            }
            unset($authGyCodeIn);
            $where['authGyCodeIn'] = implode(',', $where['authGyCodeIn']);
            $sql .= " and b.gyCode in ({$where['authGyCodeIn']}) ";
        }

        if (!empty($where['department'])) {
            // 部门的查询条件
            $bind[':a_department'] = $where['department'];
            $sql .= " and a.caiwuDept = :a_department ";
        }
        if (!empty($where['platform'])) {
            // 渠道的查询条件
            $bind[':a_platform'] = $where['platform'];
            $sql .= " and a.platform = :a_platform ";
        }
        if (!empty($where['mallName'])) {
            // 店铺名字的查询条件
            $bind[':a_mallName'] = $where['mallName'];
            $sql .= " and a.mallName = :a_mallName ";
        }
        if (!empty($where['mallId'])) {
            // 店铺ID的查询条件
            $bind[':a_mallId'] = $where['mallId'];
            $sql .= " and a.mallId = :a_mallId ";
        }
        if (!empty($where['mallGroup'])) {
            // 店铺分组的查询条件
            $bind[':b_mallGroup'] = $where['mallGroup'];
            $sql .= " and b.mallGroup = :b_mallGroup ";
        }
        if (!empty($where['mallCompany'])) {
            // 店铺分组的查询条件
            $bind[':a_mallCompany'] = $where['mallCompany'];
            $sql .= " and a.mallCompany = :a_mallCompany ";
        }
        if (User::checkUserAclRoutes($uid, 'PRODUCT_PIAOYING')) {
            $bind[':b_mallBrand'] = Brand::PIAOYING;
            $sql .= " and b.brand = :b_mallBrand ";
        }

        $sql .= " ORDER BY b.ordr asc";

        return [
            'sql' => $sql,
            'bind' => $bind
        ];
    }

    /**
     * 合并店铺数据
     */
    public function mergeMallData($list)
    {
        $finalList = [];

        foreach ($list as $item) {
            $code = empty($item['gyCode']) ? $item['mallId'] : $item['gyCode'];
            if (isset($finalList[$code])) {
                foreach ($item as $key => $value) {
                    if ($key == 'total') {
                        $finalList[$code][$key] = $value;
                        continue;
                    }
                    // 字符串类型，如果不为空跳过，否则赋值
                    if (!empty($finalList[$code][$key])) {
                        continue;
                    }
                    $finalList[$code][$key] = $value;
                }
            } else {
                $finalList[$code] = $item;
            }
        }
        return $finalList;
    }

    //
    public function getPerformanceDaysCount($where, $date, $uid)
    {
        // 店铺汇总
        $res = $this->getPerformanceDaysCountSql($where, $date, $uid);
        // ->getRawSql();
        $mallTotal = Yii::$app->db->createCommand($res['sql'], $res['bind'])->queryAll();

        // 集成天猫超市数据
        $mallTotal = $this->integrateTmallSupermarketData($mallTotal, $where, $date);

        $mallTotal = $this->mergeMallData($mallTotal);

        $mallTotal = $this->getPerformanceDisposalData($mallTotal, $where);
        // 全部、直营、非直营 的过滤条件
        $mallTotal = $this->directSalesTypeWhere($mallTotal, $where);

        // 部门汇总 department
        $array = $this->getPerformanceDepCount($mallTotal, $date);
        $mallDepTotal = $array['data'];
        // 渠道汇总 platform
        $array = $this->getPerformancePlatformCount($mallTotal, $date);
        $mallPlatformTotal = $array['data'];

        // 组别汇总 mallGroup  getPerformanceMallGroupCount
        $array = $this->getPerformanceMallGroupCount($mallTotal, $date);
        $mallGroupTotal = $array['data'];

        // 主体汇总 mallCompany
        $array = $this->getPerformanceMallCompanyCount($mallTotal, $date);
        $mallCompanyTotal = $array['data'];

        // 全部汇总
        $array = $this->getPerformanceAllCount($mallTotal, $date);
        $mallAllTotal = $array['data'];

        // 最终汇总(全部汇总只统计展示的目标销售-没有展示全年的目标销售)
        $array = $this->getPerformanceAllTotal($where, $mallTotal, $mallAllTotal);
        $allTotal = $array['allTotal'];

        return [
            'mallTotal' => $mallTotal,
            'mallDepTotal' => $mallDepTotal,
            'mallPlatformTotal' => $mallPlatformTotal,
            'mallGroupTotal' => $mallGroupTotal,
            'mallCompanyTotal' => $mallCompanyTotal,
            'mallAllTotal' => $mallAllTotal,
            'allTotal' => $allTotal
        ];
    }

    public function getPerformanceDaysCountSql($where, $days, $uid)
    {
        // 拼多多拼接的日期字段
        $pddField = "";
        // 退款
        $pddReturnField = "";
        // '快手', '小红书', '京东', '淘系', '天猫', '抖音', '微信', '有赞' 拼接的日期字段
        $vsMallDataField = "";
        $vsReturnMallDataField = "";
        // 管易拼接的日期字段
        $guanyiField = "";
        $guanyiReturnField = "";
        // 销售四部拼接的日期字段
        $d4Field = "";
        $d4ReturnField = "";
        foreach ($days as $day) {
            // $day 2025-01-01
            $daystr = strval($day);
            $pddField .= "MAX(CASE WHEN a.statDate = '{$daystr}' THEN a.total_amount END) AS `{$daystr}`,";
            $pddReturnField .= "MAX(CASE WHEN a.statDate = '{$daystr}' THEN a.fwsj_amount END) AS `return_{$daystr}`,";
            $vsMallDataField .= "MAX(CASE WHEN b.date = '{$daystr}' THEN b.gmv END) AS `{$daystr}`,";
            $vsReturnMallDataField .= "MAX(CASE WHEN b.date = '{$daystr}' THEN b.refundAmt END) AS `return_{$daystr}`,";
            $guanyiField .= "MAX(CASE WHEN a.yearDay2 = '{$daystr}' THEN b.total_amount END) AS `{$daystr}`,";
            $guanyiReturnField .= "'-1' AS `return_{$daystr}`,";
            $d4Field .= "MAX(CASE WHEN a.yearDay2 = '{$daystr}' THEN b.total END) AS `{$daystr}`,";
            $d4ReturnField .= "'-1' AS `return_{$daystr}`,";
        }

        $bind = [
            ':sdate' => $where['sdate'],
            ':edate' => date('Y-m-d', strtotime($where['edate']) + 86400),
            ':year' => explode('-', $where['select_date'])[0],
            // 主要是去除前导零
            ':month' => (int) explode('-', $where['select_date'])[1],
        ];

        // 拼多多
        $pdd_sql = "SELECT
                a.caiwuDept caiwuDept,
                a.mallCompany,
                a.mallPlatform platform,
                a.mallId,
                a.mallName,
                {$pddField}
                {$pddReturnField}
                SUM( a.total_amount ) total,
                SUM( a.fwsj_amount ) returnAmount
            FROM 
                (SELECT
                    a.mallId,
                    m.mallName,
                    m.caiwuDept,
                    m.mallCompany,
                    m.mallPlatform,
                    a.statDate,
                    a.amount jysj_amount,
                    b.amount fwsj_amount,
                    a.amount - b.amount total_amount 
                FROM 
                    ( 
                        SELECT 
                            mallId,statDate, SUM( payOrdrAmt ) amount 
                        FROM pdd_jysj 
                        WHERE statDate >= :sdate AND statDate < :edate 
                        GROUP BY mallId, statDate 
                    ) a
                    JOIN ( 
                        SELECT mallId, statDate, SUM( sucRfOrdrAmt1d ) amount 
                        FROM pdd_fwsj 
                        WHERE statDate >= :sdate AND statDate < :edate 
                        GROUP BY mallId, statDate 
                    ) b 
                    ON a.mallId = b.mallId AND a.statDate = b.statDate 
                    JOIN pdd_mall m 
                    ON a.mallId = m.mallId AND m.mallPlatform = '拼多多' AND m.mallStatus = 1 
                ) a  
                GROUP BY 
                    a.caiwuDept,
                    a.mallCompany,
                    a.mallPlatform,
                    a.mallId,
                    a.mallName";

        // vs_mall_data(视图) '快手', '小红书', '京东', '淘系', '天猫', '抖音', '微信', '有赞'
        $vs_mall_data_sql = "SELECT 
                a.caiwuDept caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.mallId,
                a.mallName,
                {$vsMallDataField}
                {$vsReturnMallDataField}
                IFNULL( SUM( b.gmv ), 0 ) total,
                b.refundAmt returnAmount 
            FROM
                pdd_mall a
            LEFT JOIN vs_mall_data b 
            ON a.mallId = b.mallId AND b.DATE >= :sdate AND b.DATE < :edate 
            WHERE a.mallStatus = 1 AND a.mallPlatform IN ( '快手', '小红书', '京东', '淘系', '天猫', '抖音', '微信' ) 
            GROUP BY
                a.caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.mallId,
                a.mallName";



        // 管易 
        $guanyi_sql = "SELECT
                CASE WHEN a.caiwuDept = '其他' OR a.caiwuDept = '销售四部' OR a.caiwuDept = '有赞微商城' THEN '创新事业部' ELSE '' END AS caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                CASE WHEN a.shop_code = '32' THEN '53061284' ELSE a.shop_code END AS mallId,
                a.shop_name,
                {$guanyiField}
                {$guanyiReturnField}
                IFNULL( SUM( b.total_amount ), 0 ) total,
                '-1' returnAmount 
            FROM(
                SELECT
                    a.type_name caiwuDept,
                    a.mallCompany,
                    a.mallPlatform,
                    a.CODE shop_code,
                    a.NAME shop_name,
                    b.yearDay2 
                FROM guanyierp_shop a 
                CROSS JOIN yearDay b 
                ON dayTime >= :sdate AND dayTime < :edate 
                WHERE is_gmv_gz_jxs = 1 
                ORDER BY b.yearDay2 
            ) a
            LEFT JOIN ( 
                SELECT
                    a.shop_code,
                    a.shop_name,
                    DATE ( delivery_date ) statDate,
                    sum( b.amount_after ) total_amount 
                FROM guanyierp_trade_deliverys_2 a 
                JOIN guanyierp_trade_deliverys_details b ON a.id = b.pid 
                WHERE a.delivery_date >= :sdate AND a.delivery_date < :edate 
                GROUP BY a.shop_code,a.shop_name,DATE ( delivery_date ) 
            ) b 
            ON a.shop_code = b.shop_code AND a.yearDay2 = b.statDate 
            GROUP BY
                a.caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.shop_code,
                a.shop_name";

        // 销售四部 
        $d4_sql = "SELECT
                CASE WHEN a.caiwuDept = '其他' OR a.caiwuDept = '销售四部' THEN '创新事业部' ELSE a.caiwuDept END AS caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.shop_code,
                a.shop_name,
                {$d4Field}
                {$d4ReturnField}
                SUM( b.total ) total,
                '-1' returnAmount  
            FROM(
                SELECT b.*,yearDay2 
                FROM yearDay a
                CROSS JOIN 
                    (SELECT 
                        '创新事业部' caiwuDept,
                        '智颂' mallCompany,
                        '经销客户' mallPlatform,
                        'lintugen' shop_code,
                        '林兔根经理商' shop_name 
                    ) b 
                    WHERE a.dayTime >= :sdate AND a.dayTime < :edate 
            ) a 
            LEFT JOIN guanyierp_trade_deliverys_32 b 
            ON a.yearDay2 = b.statDate AND b.statDate >= :sdate AND b.statDate < :edate 
            GROUP BY
                a.caiwuDept,
                a.mallCompany,
                a.mallPlatform,
                a.shop_code,
                a.shop_name ";

        $noMallGroupDisposeSql = $this->noMallGroupDisposeSql($bind[':year'], $bind[':month']);

        $sql = "SELECT a.*,b.mallPlatform as platform, b.mallCompany as mallCompany,b.caiwuDept department,b.mallGroup,b.createTime as createTime,IFNULL(b.gyCode,a.mallId) as gyCode,b.dest,IFNULL(b.ordr,999999) as `order`,b.enable as `enable`,b.mallStatus as mallStatus,ROUND( a.total * 100 / b.dest, 2 ) progress 
            FROM ( 
                {$pdd_sql} 
                UNION 
                {$vs_mall_data_sql} 
                UNION 
                {$guanyi_sql} 
                UNION 
                {$d4_sql} 
            ) a 
            LEFT JOIN (
                SELECT 
                    m.mallPlatform,
                    m.mallCompany,
                    m.caiwuDept,
                    m.mallId,
                    m.mallName,
                    m.gyName,
                    m.gyCode,
                    m.mallGroup,
                    m.ordr,
                    m.enable,
                    m.mallStatus,
                    m.brand,
                    bag.shop_sales_volume_goal dest,
                    m.createTime 
                FROM pdd_mall m 
                LEFT JOIN business_annual_planning bag 
                ON m.gyCode = bag.shop_code AND bag.year = :year AND bag.month = :month 
                WHERE m.mallStatus = 1 AND m.enable=1 
                UNION 
                {$noMallGroupDisposeSql}
            ) b 
            ON a.mallId = b.mallId ";

        if ($where['auth'] === true) {
            // (超管+负责人)、运营人员
            $sql .= " where 1 = 1 and (b.createTime < :edate or a.total > 0) ";
        } else {
            // 没有权限的直接返回空数组
            $sql .= " where 1 != 1 ";
        }

        if (isset($where['authGyCodeIn']) && count($where['authGyCodeIn']) >= 1) {
            // 运营人员 查看指定店铺的条件
            foreach ($where['authGyCodeIn'] as $key => &$authGyCodeIn) {
                // $authGyCodeIn = "'{$authGyCodeIn}'";
                $bind[':b_gyCode_' . $key] = $authGyCodeIn;
                $authGyCodeIn = ':b_gyCode_' . $key;
            }
            unset($authGyCodeIn);
            $where['authGyCodeIn'] = implode(',', $where['authGyCodeIn']);
            $sql .= " and b.gyCode in ({$where['authGyCodeIn']}) ";
        }
        if (!empty($where['department'])) {
            // 部门的查询条件
            $bind[':a_department'] = $where['department'];
            $sql .= " and a.caiwuDept = :a_department ";
        }
        if (!empty($where['platform'])) {
            // 渠道的查询条件
            $bind[':a_platform'] = $where['platform'];
            $sql .= " and a.platform = :a_platform ";
        }
        if (!empty($where['mallName'])) {
            // 店铺名字的查询条件
            $bind[':a_mallName'] = $where['mallName'];
            $sql .= " and a.mallName = :a_mallName ";
        }
        if (!empty($where['mallId'])) {
            // 店铺ID的查询条件
            $bind[':a_mallId'] = $where['mallId'];
            $sql .= " and a.mallId = :a_mallId ";
        }
        if (!empty($where['mallGroup'])) {
            // 店铺分组的查询条件
            $bind[':b_mallGroup'] = $where['mallGroup'];
            $sql .= " and b.mallGroup = :b_mallGroup ";
        }
        if (!empty($where['mallCompany'])) {
            // 店铺分组的查询条件
            $bind[':a_mallCompany'] = $where['mallCompany'];
            $sql .= " and a.mallCompany = :a_mallCompany ";
        }
        if (User::checkUserAclRoutes($uid, 'PRODUCT_PIAOYING')) {
            $bind[':b_mallBrand'] = Brand::PIAOYING;
            $sql .= " and b.brand = :b_mallBrand ";
        }
        $sql .= " ORDER BY b.ordr asc ";

        return [
            'sql' => $sql,
            'bind' => $bind
        ];
    }

    /**
     * 获取部门统计数据
     */
    private function getPerformanceDepCount($data, $date)
    {
        if (empty($data[0])) {
            return [
                'data' => [],
                'data1' => []
            ];
        }
        $template = $data[0];
        foreach ($template as &$value) {
            $value = '';
            if (in_array($value, $date)) {
                $value = 0;
            }
        }
        unset($value);

        $array = [];
        foreach ($data as $value) {
            $department_name = $value['department'];
            if (empty($array[$department_name])) {
                $template['department'] = $department_name;
                $array[$department_name] = $template;
            }
            foreach ($date as $dateItem) {
                $array[$department_name][$dateItem] = bcadd($array[$department_name][$dateItem], $value[$dateItem], 2);
            }
            $array[$department_name]['total'] = bcadd($array[$department_name]['total'], $value['total'], 2);
            // business_goal_data_record(业务目标数据表) SUM(g.month_sales_target_amount月目标销售额) dest 
            $array[$department_name]['dest'] = bcadd($array[$department_name]['dest'], $value['dest'], 2);
        }

        // 百分比计算
        foreach ($array as &$arr) {
            if (empty($arr['total']) || empty($arr['dest']) || (int) $arr['total'] == 0 || (int) $arr['dest'] == 0) {
                // 除0的情况
                $arr['progress'] = '0';
            } else {
                $arr['progress'] = round(bcdiv($arr['total'], $arr['dest'], 5) * 100, 2);
            }
        }
        unset($arr);

        return [
            'data' => array_values($array),
            'data1' => $array
        ];
    }

    /**
     * 获取渠道统计数据 platform
     * */
    private function getPerformancePlatformCount($data, $date)
    {
        if (empty($data[0])) {
            return [
                'data' => [],
                'data1' => []
            ];
        }
        $template = $data[0];
        foreach ($template as &$value) {
            $value = '';
            if (in_array($value, $date)) {
                $value = 0;
            }
        }
        unset($value);

        $array = [];
        // 全部销售额汇总
        $total = 0;
        foreach ($data as $value) {
            $platform_name = $value['platform'];
            if (empty($array[$platform_name])) {
                $template['platform'] = $platform_name;
                $array[$platform_name] = $template;
            }
            foreach ($date as $dateItem) {
                $array[$platform_name][$dateItem] = bcadd($array[$platform_name][$dateItem], $value[$dateItem], 2);
            }
            $array[$platform_name]['total'] = bcadd($array[$platform_name]['total'], $value['total'], 2);
            $total = bcadd($total, $value['total'], 2);
            // business_goal_data_record(业务目标数据表) SUM(g.month_sales_target_amount月目标销售额) dest 
            $array[$platform_name]['dest'] = bcadd($array[$platform_name]['dest'], $value['dest'], 2);
        }

        // 渠道比例百分比计算
        foreach ($array as &$arr) {
            if (empty($arr['total']) || empty($total) || (int) $arr['total'] == 0 || (int) $total == 0) {
                // 除0的情况
                $arr['progress'] = '0';
            } else {
                $arr['progress'] = round(bcdiv($arr['total'], $total, 5) * 100, 2);
            }
        }
        unset($arr);

        $sort = ['天猫', '淘系', '拼多多', '抖音', '快手', '微信', '有赞', '经销客户', 'CS渠道', '加工客户', '小红书', '京东'];

        $arr = [];
        foreach ($sort as $value) {
            if (isset($array[$value])) {
                $arr[$value] = $array[$value];
                unset($array[$value]);
                continue;
            }
        }
        $arr = array_merge($arr, $array);

        return [
            'data' => array_values($arr),
            'data1' => $arr
        ];
    }

    /**
     * 获取组别统计数据 mallGroup
     * */
    private function getPerformanceMallGroupCount($data, $date)
    {
        if (empty($data[0])) {
            return [
                'data' => [],
                'data1' => []
            ];
        }

        // 组别排序
        $arr1 = $this->mallGroupSort;
        foreach ($data as $mall) {
            if (isset($arr1[$mall['mallGroup']])) {
                $arr1[$mall['mallGroup']][] = $mall;
            } else {
                $arr1['其他组'][] = $mall;
            }
        }
        $merge = [];
        foreach ($arr1 as $mallGroupArr) {
            $merge = array_merge($merge, $mallGroupArr);
        }
        $data = $merge;

        $template = $data[0];
        foreach ($template as &$value) {
            $value = '';
            if (in_array($value, $date)) {
                $value = 0;
            }
        }
        unset($value);

        $array = [];
        foreach ($data as $value) {
            $mall_group = $value['mallGroup'];

            if ($mall_group == 'null' || empty($mall_group)) {
                $mall_group = '其他分组';
            }

            if (empty($array[$mall_group])) {
                $template['mallGroup'] = $mall_group;
                $array[$mall_group] = $template;
            }
            foreach ($date as $dateItem) {
                $array[$mall_group][$dateItem] = bcadd($array[$mall_group][$dateItem], $value[$dateItem], 2);
            }
            // department
            if (empty($array[$mall_group]['department'])) {
                $array[$mall_group]['department'] = [];
            }

            $array[$mall_group]['department'][] = $value['department'];
            $array[$mall_group]['total'] = bcadd($array[$mall_group]['total'], $value['total'], 2);
            // business_goal_data_record(业务目标数据表) SUM(g.month_sales_target_amount月目标销售额) dest 
            $array[$mall_group]['dest'] = bcadd($array[$mall_group]['dest'], $value['dest'], 2);
        }

        // 百分比计算
        foreach ($array as &$arr) {
            // 去重 
            $arr['department'] = array_values(array_unique($arr['department']));
            if (empty($arr['total']) || empty($arr['dest']) || (int) $arr['total'] == 0 || (int) $arr['dest'] == 0) {
                // 除0的情况
                $arr['progress'] = '0';
            } else {
                $arr['progress'] = round(bcdiv($arr['total'], $arr['dest'], 5) * 100, 2);
            }
        }
        unset($arr);

        return [
            'data' => array_values($array),
            'data1' => $array
        ];
    }

    /**
     * 获取主体统计数据 mallCompany
     * */
    private function getPerformanceMallCompanyCount($data, $date)
    {
        if (empty($data[0])) {
            return [
                'data' => [],
                'data1' => []
            ];
        }

        $template = $data[0];
        foreach ($template as &$value) {
            $value = '';
            if (in_array($value, $date)) {
                $value = 0;
            }
        }
        unset($value);

        $array = [];
        foreach ($data as $value) {
            $mallCompany = $value['mallCompany'];

            if ($mallCompany == 'null' || empty($mallCompany)) {
                $mallCompany = '其他主体';
            }

            if (empty($array[$mallCompany])) {
                $template['mallCompany'] = $mallCompany;
                $array[$mallCompany] = $template;
            }
            foreach ($date as $dateItem) {
                $array[$mallCompany][$dateItem] = bcadd($array[$mallCompany][$dateItem], $value[$dateItem], 2);
            }
            $array[$mallCompany]['total'] = bcadd($array[$mallCompany]['total'], $value['total'], 2);
            // business_goal_data_record(业务目标数据表) SUM(g.month_sales_target_amount月目标销售额) dest 
            $array[$mallCompany]['dest'] = bcadd($array[$mallCompany]['dest'], $value['dest'], 2);
        }

        // 百分比计算
        foreach ($array as &$arr) {
            if (empty($arr['total']) || empty($arr['dest']) || (int) $arr['total'] == 0 || (int) $arr['dest'] == 0) {
                // 除0的情况
                $arr['progress'] = '0';
            } else {
                $arr['progress'] = round(bcdiv($arr['total'], $arr['dest'], 5) * 100, 2);
            }
        }
        unset($arr);

        return [
            'data' => array_values($array),
            'data1' => $array
        ];
    }

    /**
     * 获取全部汇总的统计数据 
     * */
    private function getPerformanceAllCount($data, $date)
    {
        if (empty($data[0])) {
            return [
                'data' => [],
                'data1' => []
            ];
        }
        $template = $data[0];
        foreach ($template as &$value) {
            $value = '';
            if (in_array($value, $date)) {
                $value = 0;
            }
        }
        unset($value);

        $countRow = $template;
        $countRow['directSalesTotal'] = 0;
        foreach ($data as $value) {
            foreach ($date as $dateItem) {
                $countRow[$dateItem] = bcadd($countRow[$dateItem], $value[$dateItem], 2);
            }
            $countRow['total'] = bcadd($countRow['total'], $value['total'], 2);

            if ($value['directSalesType'] == 1) {
                // 直营店铺的统计
                $countRow['directSalesTotal'] = bcadd($countRow['directSalesTotal'], $value['total'], 2);
            }

            // business_goal_data_record(业务目标数据表) SUM(g.month_sales_target_amount月目标销售额) dest 
            $countRow['dest'] = bcadd($countRow['dest'], $value['dest'], 2);
        }

        // 百分比计算
        if (empty($countRow['total']) || empty($countRow['dest']) || (int) $countRow['total'] == 0 || (int) $countRow['dest'] == 0) {
            // 除0的情况
            $countRow['progress'] = '0';
        } else {
            $countRow['progress'] = round(bcdiv($countRow['total'], $countRow['dest'], 5) * 100, 2);
        }

        return [
            'data' => $countRow
        ];
    }

    /**
     * 最终汇总(全部汇总只统计展示的目标销售-没有展示全年的目标销售)
     */
    private function getPerformanceAllTotal($where, $mallTotal, $mallAllTotal)
    {
        if (strlen($where['select_date']) == 4) {
            $allTotal = [
                'dest' => $mallAllTotal['dest'] ?? 0,
                'progress' => $mallAllTotal['progress'] ?? 0,
                'total' => $mallAllTotal['total'] ?? 0,
                'directSalesTotal' => $mallAllTotal['directSalesTotal'] ?? 0
            ];

            return [
                'allTotal' => $allTotal
            ];
        } else {
            // 月份的最终统计
            $allTotal = [
                'dest' => $mallAllTotal['dest'] ?? 0,
                'progress' => $mallAllTotal['progress'] ?? 0,
                'total' => $mallAllTotal['total'] ?? 0,
                'directSalesTotal' => $mallAllTotal['directSalesTotal'] ?? 0
            ];

            return [
                'allTotal' => $allTotal
            ];
        }
    }

    /**
     * 对返回的数据进行整理
     */
    private function getPerformanceDisposalData($mallTotal, $where)
    {
        $date = $where['select_date'];
        // 删除闭店 
        $arr = ['05.02.014', '05.02.013', 'XHSCLOSED01', '85', '88', '89', '90', '111'];

        $array = [];
        foreach ($mallTotal as $mall) {
            $mallId = $mall['mallId'];
            if (!in_array($mallId, $arr)) {
                $array[] = $mall;
            }
        }

        // 对没有分组数据的进行更新
        // $array = $this->noMallGroupDispose($array);

        // 排序
        $arr1 = [];
        $arr999999 = [];
        foreach ($array as $mall) {
            if ($mall['order'] == 999999 || $mall['order'] >= 999000) {
                $arr999999[] = $mall;
            } else {
                $arr1[] = $mall;
            }
        }
        $array = array_merge($arr1, $arr999999);

        // 部门的排序 
        $arr = $this->departmentSort;

        foreach ($array as $mall) {
            if (isset($arr[$mall['department']])) {
                $arr[$mall['department']][] = $mall;
            } else {
                $arr['其他部门'][] = $mall;
            }
        }

        foreach ($arr as &$mallArr) {
            $arr1 = $this->mallGroupSort;
            foreach ($mallArr as $mall) {
                if (isset($arr1[$mall['mallGroup']])) {
                    $arr1[$mall['mallGroup']][] = $mall;
                } else {
                    $arr1['其他组'][] = $mall;
                }
            }
            $merge = [];
            foreach ($arr1 as $mallGroupArr) {
                $merge = array_merge($merge, $mallGroupArr);
            }
            $mallArr = $merge;
        }
        unset($mallArr);

        $merge = [];
        foreach ($arr as $departmentMallArray) {
            $merge = array_merge($merge, $departmentMallArray);
        }

        $array = $merge;

        // 直营和非直营的标识 
        $indirectSalesGroup = (new Query())->from("pdd_mall_indirect_sales_group_config")->select('name')->column();
        foreach ($array as &$row) {
            // direct_sales_type 1 直营 2 非直营
            $row['directSalesType'] = 1;
            if (empty($indirectSalesGroup)) {
                // 没有非直营的配置 全部直营
                continue;
            }
            $mallGroup = trim($row['mallGroup']);
            if (in_array($mallGroup, $indirectSalesGroup)) {
                // 非直营
                $row['directSalesType'] = 2;
            }
        }
        unset($row);

        // 获取冻结的历史数据
        if (strlen($date) == 4 && $date != date("Y")) {
            // 表示过去的年份，提取冻结数据
            $freezeData = (new Query())->from('shop_summarizing_label_record')
                ->where(['=', 'type', 1])
                ->andWhere(['=', 'year', $date])
                ->all();
        } else if (strlen($date) == 7) {
            // 表示过去月份，提取冻结数据
            $arr = explode('-', $date);
            $year = $arr[0] ?? '';
            $month = $arr[1] ?? '';
            $freezeData = (new Query())->from('shop_summarizing_label_record')
                ->where(['=', 'type', 2])
                ->andWhere(['=', 'year', $year])
                ->andWhere(['=', 'month', $month])
                ->all();
        }

        if (empty($freezeData)) {
            return $array;
        }

        $freezeData = array_combine(array_column($freezeData, 'mall_id'), $freezeData);
        foreach ($array as &$row) {
            $mall_id = $row['mallId'];
            if (empty($freezeData[$mall_id])) {
                continue;
            }
            $row['department'] = $freezeData[$mall_id]['department'];
            $row['mallCompany'] = $freezeData[$mall_id]['mall_company_abbr'];
            $row['mallGroup'] = $freezeData[$mall_id]['mall_group'];
            $row['mallName'] = $freezeData[$mall_id]['mall_name'];
            $row['platform'] = $freezeData[$mall_id]['platform'];
        }
        unset($row);

        return $array;
    }

    /**
     * 直营条件的过滤 代码处理
     */
    private function directSalesTypeWhere($mallTotal, $where)
    {
        // 直营类型 1 直营 2 非直营 3 全部
        $directSalesType = $where['directSalesType'];
        if ($directSalesType == '3') {
            return $mallTotal;
        }

        $array = [];
        foreach ($mallTotal as $row) {
            if ((int) $row['directSalesType'] == (int) $directSalesType) {
                $array[] = $row;
            }
        }
        return $array;
    }

    /**
     * 返回没有店铺分组的sql
     */
    public function noMallGroupDisposeSql($year, $month = 0)
    {
        // FAKE_0001(经销1组), FAKE_0002(经销2组), FAKE_0003(经销3组), FAKE_0005(加工)
        // 获取目标销售额
        $query = (new Query())->from("business_annual_planning")
            ->select("shop_code,year,month,SUM(shop_sales_volume_goal) as shop_sales_volume_goal")
            ->where(['in', 'shop_code', ['FAKE_0001', 'FAKE_0002', 'FAKE_0003', 'FAKE_0005']])
            ->andWhere(["=", "year", $year])
            ->andWhere(["is", "deleted_time", null]);
        if (empty($month)) {
            // 表示提取年份的目标数据
            $query = $query->groupBy("shop_code,year");
        } else {
            // 表示提取月份的目标数据
            $query = $query->groupBy("shop_code,year,month");
        }
        $businessAnnualPlanning = $query->all();

        // 获取店铺目标销售额
        $getShopAalesVolumeGoal = function ($businessAnnualPlanning, $shopCode, $shopNumber) use ($year, $month) {
            // $shopNumber 店铺数量 类似经销1组 并非一家店铺 而是多家店铺组成
            if (empty($month)) {
                // 表示年份
                foreach ($businessAnnualPlanning as $value) {
                    if ($value['shop_code'] == $shopCode && $value['year'] == $year) {
                        if (empty($value['shop_sales_volume_goal'])) {
                            return 0;
                        }
                        return bcdiv($value['shop_sales_volume_goal'], $shopNumber);
                    }
                }
            } else {
                // 月份查询
                foreach ($businessAnnualPlanning as $value) {
                    if ($value['shop_code'] == $shopCode && $value['year'] == $year && $value['month'] == $month) {
                        if (empty($value['shop_sales_volume_goal'])) {
                            return 0;
                        }
                        return bcdiv($value['shop_sales_volume_goal'], $shopNumber);
                    }
                }
            }
        };

        $sql = [];
        $dest = $getShopAalesVolumeGoal($businessAnnualPlanning, 'FAKE_0001', 17);
        $sql[] = " select 
                    '经销客户' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '117' as mallId, 
                    '河南林城商贸有限公司' as mallName, 
                    '河南林城商贸有限公司' as gyName, 
                    '117' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999100' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '118' as mallId, 
                    '福州图雅贸易有限公司' as mallName, 
                    '福州图雅贸易有限公司' as gyName, 
                    '118' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999100' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    '经销客户' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '108' as mallId, 
                    '上海安洋通华供应链管理有限公司' as mallName, 
                    '上海安洋通华供应链管理有限公司' as gyName, 
                    '108' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999100' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    '经销客户' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '120' as mallId, 
                    '广州聚优阁品牌有限公司' as mallName, 
                    '广州聚优阁品牌有限公司' as gyName, 
                    '120' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999100' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    '经销客户' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '129' as mallId, 
                    '杭州创醒供应链管理有限公司' as mallName, 
                    '杭州创醒供应链管理有限公司' as gyName, 
                    '129' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999100' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    '经销客户' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '130' as mallId, 
                    '广州新合品牌管理有限公司' as mallName, 
                    '广州新合品牌管理有限公司' as gyName, 
                    '130' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999100' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '135' as mallId, 
                    '沈阳康颜化妆品有限公司' as mallName, 
                    '沈阳康颜化妆品有限公司' as gyName, 
                    '135' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999110' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-05-22 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '134' as mallId, 
                    '天津市鑫化城商贸有限公司' as mallName, 
                    '天津市鑫化城商贸有限公司' as gyName, 
                    '134' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999110' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-05-22 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '133' as mallId, 
                    '沧州恒仲商贸有限公司' as mallName, 
                    '沧州恒仲商贸有限公司' as gyName, 
                    '133' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999110' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-05-22 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '132' as mallId, 
                    '河北伽芬商贸有限公司' as mallName, 
                    '河北伽芬商贸有限公司' as gyName, 
                    '132' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999110' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-05-22 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '131' as mallId, 
                    '福州图雅贸易有限公司' as mallName, 
                    '福州图雅贸易有限公司' as gyName, 
                    '131' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999110' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-05-22 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '136' as mallId, 
                    '昆明冉琪商贸有限公司' as mallName, 
                    '昆明冉琪商贸有限公司' as gyName, 
                    '136' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999110' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-05-22 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";

        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '138' as mallId, 
                    '未来买买供应链管理(西安)有限公司' as mallName, 
                    '未来买买供应链管理(西安)有限公司' as gyName, 
                    '138' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999111' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-06-16 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";

        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '137' as mallId, 
                    '保定市忠新美丽花都商贸有限公司' as mallName, 
                    '保定市忠新美丽花都商贸有限公司' as gyName, 
                    '137' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999112' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-06-16 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";

        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '139' as mallId, 
                    '郑州市一可商贸有限公司' as mallName, 
                    '郑州市一可商贸有限公司' as gyName, 
                    '139' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999113' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-06-16 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";

        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '144' as mallId, 
                    '长春市美绘化妆品有限责任公司' as mallName, 
                    '长春市美绘化妆品有限责任公司' as gyName, 
                    '144' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999113' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-07-21 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";

        $sql[] = " select 
                    'CS渠道' as mallPlatform,
                    '智颂' as mallCompany,
                    '广州事业部' as caiwuDept,
                    '145' as mallId, 
                    '廊坊市云雅商贸有限公司' as mallName, 
                    '廊坊市云雅商贸有限公司' as gyName, 
                    '145' as gyCode, 
                    '经销1组' as mallGroup, 
                    '999113' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-07-21 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";

        $dest = $getShopAalesVolumeGoal($businessAnnualPlanning, 'FAKE_0002', 5);
        $sql[] = " select 
                    '经销客户' as mallPlatform,
                    '智颂' as mallCompany,
                    '供应链部' as caiwuDept,
                    '83' as mallId, 
                    '深圳市十一月供应链有限公司' as mallName, 
                    '深圳市十一月供应链有限公司' as gyName, 
                    '83' as gyCode, 
                    '经销2组' as mallGroup, 
                    '999200' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    '经销客户' as mallPlatform,
                    '智颂' as mallCompany,
                    '供应链部' as caiwuDept,
                    '95' as mallId, 
                    '石春静' as mallName, 
                    '石春静' as gyName, 
                    '95' as gyCode, 
                    '经销2组' as mallGroup, 
                    '999200' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    '加工客户' as mallPlatform,
                    '股份' as mallCompany,
                    '供应链部' as caiwuDept,
                    '109' as mallId, 
                    '杭州妍创品牌管理有限公司' as mallName, 
                    '杭州妍创品牌管理有限公司' as gyName, 
                    '109' as gyCode, 
                    '经销2组' as mallGroup, 
                    '999200' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    '经销客户' as mallPlatform,
                    '智颂' as mallCompany,
                    '供应链部' as caiwuDept,
                    '114' as mallId, 
                    '广州迪顺化妆品有限公司' as mallName, 
                    '广州迪顺化妆品有限公司' as gyName, 
                    '114' as gyCode, 
                    '经销2组' as mallGroup, 
                    '999200' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    '经销客户' as mallPlatform,
                    '股份' as mallCompany,
                    '供应链部' as caiwuDept,
                    '05.02.015' as mallId, 
                    '暮霖（广州）科技文化传媒有限公司（伟实）' as mallName, 
                    '暮霖（广州）科技文化传媒有限公司（伟实）' as gyName, 
                    '05.02.015' as gyCode, 
                    '经销2组' as mallGroup, 
                    '999200' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest ,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $dest = $getShopAalesVolumeGoal($businessAnnualPlanning, 'FAKE_0003', 1);
        $sql[] = " select 
                    '经销客户' as mallPlatform,
                    '智颂' as mallCompany,
                    '供应链部' as caiwuDept,
                    'lintugen' as mallId, 
                    '林兔根经理商' as mallName, 
                    '林兔根经理商' as gyName, 
                    'lintugen' as gyCode, 
                    '经销3组' as mallGroup, 
                    '999300' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $dest = $getShopAalesVolumeGoal($businessAnnualPlanning, 'FAKE_0005', 3);
        $sql[] = " select 
                    '加工客户' as mallPlatform,
                    '股份' as mallCompany,
                    '供应链部' as caiwuDept,
                    '08' as mallId, 
                    '杭州妍创化妆品有限公司' as mallName, 
                    '杭州妍创化妆品有限公司' as gyName, 
                    '08' as gyCode, 
                    '加工' as mallGroup, 
                    '999400' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    '加工客户' as mallPlatform,
                    '股份' as mallCompany,
                    '供应链部' as caiwuDept,
                    '01.09.007' as mallId, 
                    '阿美妍生物科技（杭州）有限公司' as mallName, 
                    '阿美妍生物科技（杭州）有限公司' as gyName, 
                    '01.09.007' as gyCode, 
                    '加工' as mallGroup, 
                    '999400' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";
        $sql[] = " select 
                    '加工客户' as mallPlatform,
                    '股份' as mallCompany,
                    '供应链部' as caiwuDept,
                    '02.01.005' as mallId, 
                    '洛阳春凤商贸有限公司' as mallName, 
                    '洛阳春凤商贸有限公司' as gyName, 
                    '02.01.005' as gyCode, 
                    '加工' as mallGroup, 
                    '999400' as ordr, 
                    '1' as ENABLE, 
                    '1' as mallStatus, 
                    '1' as brand, 
                    '{$dest}' as dest,
                    STR_TO_DATE('2025-01-01 00:00:00', '%Y-%m-%d %H:%i:%s') as createTime ";

        return implode(' UNION ', $sql);
    }

    /**
     * 对没有店铺分组的店铺进行处理
     */
    public function noMallGroupDispose($array)
    {
        // 经销1组
        $arr1 = ['117', '118', '108'];
        // 经销2组
        $arr2 = ['83', '95', '109', '114', '05.02.015'];
        // 孔凤春项目组
        $arr3 = ['60d2ae64b4d8a200010fdc96'];
        // 加工
        $arr4 = ['08', '01.09.007', '02.01.005'];
        // 护肤三组 
        $arr5 = ['179538892'];
        // 经销三组
        $arr6 = ['lintugen'];
        foreach ($array as &$mall) {
            if (!empty($mall['mallGroup'])) {
                // 跳过不等于null的分组
                continue;
            }
            $mallId = $mall['mallId'];
            if (in_array($mallId, $arr1)) {
                $mall['mallGroup'] = '经销1组';
            }
            if (in_array($mallId, $arr2)) {
                $mall['mallGroup'] = '经销2组';
            }
            if (in_array($mallId, $arr3)) {
                $mall['mallGroup'] = '孔凤春项目组';
            }
            if (in_array($mallId, $arr4)) {
                $mall['mallGroup'] = '加工';
            }
            if (in_array($mallId, $arr5)) {
                $mall['mallGroup'] = '护肤三组';
            }
            if (in_array($mallId, $arr6)) {
                $mall['mallGroup'] = '经销3组';
            }
        }
        unset($mall);

        return $array;
    }

    /**
     * 天猫超市退款后销售额按天汇总
     * @param array $tmallSupermarketData 天猫超市数据
     * @param array $days 日期数组
     * @return array 按天汇总的退款后销售额数据
     */
    private function getTmallSupermarketRefundSummary($tmallSupermarketData, $days)
    {
        $summary = [];

        // 初始化每天的数据
        foreach ($days as $day) {
            $summary[$day] = [
                'pay_amount' => 0,      // 支付金额
                'refund_amount' => 0,   // 退款金额
                'net_amount' => 0,      // 退款后销售额
            ];
        }

        // 汇总每天的数据
        foreach ($tmallSupermarketData as $record) {
            // 获取统计日期
            $statDate = $record['stat_date'] ?? '';
            if (empty($statDate)) {
                continue;
            }

            // 转换日期格式：20250101 -> 2025-01-01
            $formattedDate = date('Y-m-d', strtotime($statDate));

            // 检查日期是否在汇总范围内
            if (!isset($summary[$formattedDate])) {
                continue;
            }

            // 累加支付金额（使用实际支付金额）
            $payAmount = floatval($record['real_pay_ord_amt_1d'] ?? 0);
            $summary[$formattedDate]['pay_amount'] += $payAmount;

            // 累加退款金额
            $refundAmount = floatval($record['refund_amt_1d'] ?? 0);
            $summary[$formattedDate]['refund_amount'] += $refundAmount;

            // 计算退款后销售额
            $summary[$formattedDate]['net_amount'] = $summary[$formattedDate]['pay_amount'] - $summary[$formattedDate]['refund_amount'];
        }

        return $summary;
    }

    /**
     * 集成天猫超市数据到主数据流
     * @param array $mallTotal 现有的店铺数据
     * @param array $where 查询条件
     * @param array $date 日期数组
     * @return array 集成后的数据
     */
    private function integrateTmallSupermarketData($mallTotal, $where, $date)
    {
        // 获取天猫超市数据的时间范围
        $fist_day_of_month = date('Y-m-d H:i:s', strtotime($where['sdate']));
        $last_day_of_month = date('Y-m-d H:i:s', strtotime($where['edate']) + 86400 - 1);
        $mongo_start_date = new \MongoDB\BSON\UTCDateTime(strtotime($fist_day_of_month) * 1000);
        $mongo_end_date = new \MongoDB\BSON\UTCDateTime(strtotime($last_day_of_month) * 1000);

        // 查询天猫超市数据
        $tmcs = TmallSupermarketRefundData::find()
            ->where(['>=', 'date', $mongo_start_date])
            ->andWhere(['<', 'date', $mongo_end_date])
            ->all();

        // 调试信息
        if (defined('YII_DEBUG') && YII_DEBUG) {
            Yii::info("天猫超市数据查询: 开始时间 {$fist_day_of_month}, 结束时间 {$last_day_of_month}, 找到 " . count($tmcs) . " 条记录", 'tmall-supermarket');
        }

        if (empty($tmcs)) {
            return $mallTotal;
        }

        // 按店铺和日期汇总天猫超市数据
        $tmallSupermarketSummary = $this->getTmallSupermarketDataByShop($tmcs, $date);

        // 替换现有的天猫超市店铺数据，而不是插入新数据
        foreach ($tmallSupermarketSummary as $shopData) {
            $shopId = $shopData['mallId'];

            // 查找现有的店铺数据并替换
            $found = false;
            foreach ($mallTotal as $index => $existingShop) {
                if (isset($existingShop['mallId']) && $existingShop['mallId'] == $shopId) {
                    // 保留原有的目标销售额
                    $dest = $existingShop['dest'] ?? 0;

                    // 替换现有店铺的销售和退款数据，但保留目标销售额
                    $mallTotal[$index] = array_merge($existingShop, $shopData);
                    $mallTotal[$index]['dest'] = $dest; // 确保目标销售额不被覆盖

                    // 重新计算进度
                    if (!empty($dest) && $dest > 0) {
                        $total = floatval($shopData['total'] ?? 0);
                        $mallTotal[$index]['progress'] = number_format(($total / $dest) * 100, 2, '.', '');
                    } else {
                        $mallTotal[$index]['progress'] = '0.00';
                    }

                    $found = true;
                    break;
                }
            }

            // 如果没有找到现有店铺，则添加新数据
            if (!$found) {
                $mallTotal[] = $shopData;
            }
        }

        return $mallTotal;
    }

    /**
     * 按店铺汇总天猫超市数据
     * @param array $tmallSupermarketData 天猫超市原始数据
     * @param array $date 日期数组
     * @return array 按店铺汇总的数据
     */
    private function getTmallSupermarketDataByShop($tmallSupermarketData, $date)
    {
        $shopSummary = [];

        // 按店铺分组汇总数据
        foreach ($tmallSupermarketData as $record) {
            $shopId = $record['supplier_code'] ?? '';

            if (empty($shopId)) {
                continue;
            }

            // 初始化店铺数据
            if (!isset($shopSummary[$shopId])) {
                $shopSummary[$shopId] = [
                    'mallId' => $shopId,
                    'directSalesType' => 1, // 设置为直营类型
                    'total' => 0,
                    'returnAmount' => 0,
                ];

                // 初始化每天的数据
                foreach ($date as $day) {
                    $shopSummary[$shopId][$day] = 0;
                    $shopSummary[$shopId]['return_' . $day] = 0;
                }
            }

            // 获取统计日期
            $statDate = $record['stat_date'] ?? '';
            if (empty($statDate)) {
                continue;
            }

            // 转换日期格式：20250101 -> 2025-01-01
            $formattedDate = date('Y-m-d', strtotime($statDate));

            // 检查日期格式，判断是日度汇总还是月度汇总
            $dateKey = $formattedDate;
            $isMonthlyAggregation = false;

            // 如果 $date 数组中包含 YYYY-MM 格式，说明是月度汇总
            if (!empty($date) && preg_match('/^\d{4}-\d{2}$/', $date[0])) {
                $isMonthlyAggregation = true;
                $dateKey = date('Y-m', strtotime($statDate)); // 转换为 YYYY-MM 格式
            }

            // 检查日期是否在汇总范围内
            if (!in_array($dateKey, $date)) {
                continue;
            }

            // 计算退款后销售额（实际支付金额 - 退款金额）
            // $payAmount = floatval($record['pay_ord_amt_1d'] ?? 0);
            $refundAmount = floatval($record['refund_amt_1d'] ?? 0);
            $netAmount = floatval($record['real_pay_ord_amt_1d'] ?? 0);

            // 累加到对应日期（使用正确的日期键）
            $shopSummary[$shopId][$dateKey] += $netAmount;
            $shopSummary[$shopId]['return_' . $dateKey] += $refundAmount;

            // 累加总计
            $shopSummary[$shopId]['total'] += $netAmount;
            $shopSummary[$shopId]['returnAmount'] += $refundAmount;

        }

        // 格式化所有金额为保留2位小数的字符串
        foreach ($shopSummary as &$shop) {
            // 格式化总计金额
            $shop['total'] = number_format($shop['total'], 2, '.', '');
            $shop['returnAmount'] = number_format($shop['returnAmount'], 2, '.', '');

            // 格式化每日金额
            foreach ($shop as $key => &$value) {
                if (is_numeric($value) && (preg_match('/^\d{4}-\d{2}-\d{2}$/', $key) || strpos($key, 'return_') === 0)) {
                    $value = number_format($value, 2, '.', '');
                }
            }
        }

        return array_values($shopSummary);
    }
}
